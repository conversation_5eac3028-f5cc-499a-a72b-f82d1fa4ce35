<script setup lang="ts">
import { PlusOutlined } from '@ant-design/icons-vue';
import { AnnualPlanTypeStateConstant, IAnnualPlanTypeListResponse } from '@haierbusiness-front/common-libs';
import { getCurrentRouter, resolveParam, routerParam } from '@haierbusiness-front/utils';
import {
  FormInstance,
  Button as hButton,
  Col as hCol,
  Collapse as hCollapse,
  CollapsePanel as hCollapsePanel,
  DatePicker as hDatePicker,
  Form as hForm,
  Popconfirm as hPopconfirm,
  FormItem as hFormItem,
  Row as hRow,
  Spin as hSpin,
  message,
} from 'ant-design-vue';
import { computed, ref, watch, provide, inject } from 'vue';

import { dailyAnnualPlanApi } from '@haierbusiness-front/apis/src/daily/annual/plan/plan';
import { dailyTypeApi } from '@haierbusiness-front/apis/src/daily/type/type';
import {
  EvaluateControlConstant,
  EvaluateStateConstant,
  EvaluateTypeConstant,
  IAnnualPlanDetailRequestDTO,
  IAnnualPlanDetailResponseDTO,
  IAnnualPlanSaveOrUpdateRequestDTO,
  IAnnualPlanTypeListRequest,
  IDailyPersonalResponse,
  IEvaluateSaveRequestDTO,
  IMonthPlanDetailResponseDTO,
  PlanTypeConstant,
} from '@haierbusiness-front/common-libs/src/daily';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import globalPinia from '@haierbusiness-front/utils/src/store/store';
import dayjs, { Dayjs } from 'dayjs';
import { storeToRefs } from 'pinia';
import updateYearPlan from './updateYearPlan.vue';
import updateYearTarget from './updateYearTarget.vue';
import evaluate from './evaluate.vue';
import { evaluateApi } from '@haierbusiness-front/apis';
import { getDailyPersonUser } from '../../../utils/dailyPersonUtil';
const { loginUser } = storeToRefs(applicationStore(globalPinia));

const router = getCurrentRouter();
const prop = defineProps({
  query: Object,
});

const existYears = computed(() => <string | undefined>resolveParam(prop?.query?.existYears));
/*
 * edit 修改模式
 * add 新增
 * adjust 调整(生效后)
 * resetting 重制作模式(生效后)
 * summarize 总结模式(可评价状态)
 * evaluate  评价模式(可评价状态)
 * planform-evaluate  平台评价模式(可评价状态)
 *
 */
const type = computed(() => <string | undefined>resolveParam(prop?.query?.type));

/**
 * 展示临时项目
 */
const showTemp = computed(() => <string | undefined>resolveParam(prop?.query?.showTemp));
/**
 * evaluate模式时使用
 */
const evaluateType = computed(() => <string | undefined>resolveParam(prop?.query?.evaluateType));
const evaluateGroupFlag = computed(() => <string | undefined>resolveParam(prop?.query?.evaluateGroupFlag));

const evaluateTypeName = computed(() => {
  return EvaluateTypeConstant.ofCode(parseInt(evaluateType.value as any))?.desc;
});

const dailyPersonUser = ref<IDailyPersonalResponse>();
const refreshDailyPersonUser = () => {
  getDailyPersonUser(loginUser?.value?.username!!).then(
    (it) => (
      (dailyPersonUser.value = it),
      (formParam.value.deptCode = dailyPersonUser?.value?.deptCode),
      (formParam.value.deptName = dailyPersonUser?.value?.deptName)
    ),
  );
};
{
  refreshDailyPersonUser();
}

// 修改模式时使用的id
const id = computed(() => <string | undefined>resolveParam(prop?.query?.id));
const month = computed(() => <string | undefined>resolveParam(prop?.query?.month));
const formParam = ref<IAnnualPlanSaveOrUpdateRequestDTO>({
  year: undefined,
  deptName: dailyPersonUser?.value?.deptCode,
  deptCode: dailyPersonUser?.value?.deptName,
  items: [],
});

const submitTitle = computed(() => {
  if (type.value === 'adjust') {
    return '是否确认提交?';
  } else if (type.value === 'summarize' || type.value === 'evaluate' || type.value === 'planform-evaluate') {
    return '是否确认提交?';
  } else {
    return '提交后将开启年度目标审批，是否确认提交?';
  }
});
// 编辑时查询数据
const data = ref<IAnnualPlanDetailResponseDTO>();
const dailyAnnualPlanDetailParam = ref<IAnnualPlanDetailRequestDTO>({
  evaluateControl: EvaluateControlConstant.ALL.code,
});
const dailyAnnualPlanDetailApiRun = (id: number) => {
  loadLoading.value = true;
  dailyAnnualPlanDetailParam.value.id = id;
  dailyAnnualPlanApi
    .detail(dailyAnnualPlanDetailParam.value)
    .then((it) => {
      dailyTypeApiRun(it.year);
      data.value = it;
      year.value = String(it.year);
      convertFormParam(data.value);
    })
    .finally(() => {
      loadLoading.value = false;
    });
};
const loadLoading = ref(false);

// 查询数据转为form表单参数
const convertFormParam = (data: IAnnualPlanDetailResponseDTO) => {
  if (data) {
    formParam.value.id = data.id;
    formParam.value.year = data.year;
    formParam.value.target = data.target;
    formParam.value.orientation = data.orientation;
    formParam.value.deptCode = data.deptCode;
    formParam.value.deptName = data.deptName;
    formParam.value.items = [
      ...(data.annualPlanItems
        ?.filter((it) => {
          if (showTemp.value && showTemp.value == 'true') {
            return true;
          } else {
            return !it.temp;
          }
        })
        ?.map((it) => {
          return {
            id: it.id,
            name: it.name,
            typeId: it.typeId,
            typeName: it.typeName,
            planType: it.planType,
            planDesc: it.planDesc,
            planValue: it.planValue,
            planUnit: it.planUnit,
            monthPlans: [
              ...(it.monthPlanItems?.map((itm) => {
                const evaluates = itm.evaluate?.filter((ie) => {
                  return ie.type === EvaluateTypeConstant.MICRO_MONTH.code;
                });
                let microEvaluate = undefined;
                if (evaluates && evaluates.length > 0) {
                  microEvaluate = evaluates[0];
                }
                return {
                  id: itm.id,
                  month: itm.month,
                  apTypeId: itm.apTypeId,
                  apTypeName: itm.apTypeName,
                  principalUsercode: itm.principalUsercode,
                  principalUsername: itm.principalUsername,
                  planType: itm.planType,
                  planDesc: itm.planDesc,
                  planValue: itm.planValue,
                  state: itm.state,
                  planUnit: itm.planUnit,
                  type: itm.type,
                  typeName: itm.typeName,
                  completePlanDesc: itm.completePlanDesc,
                  completePlanTime: itm.completePlanTime,
                  completePlanValue: itm.completePlanValue,
                  completeRate: itm.completeRate,
                  evaluateId: microEvaluate?.id,
                  evaluateAmount: microEvaluate?.evaluateAmount,
                  evaluateLevel: microEvaluate?.evaluateLevel,
                  evaluateRemark: microEvaluate?.evaluateRemark,
                };
              }) as any),
            ],
          };
        }) as any),
    ];
  }
};

// 检测内容是否改变
const isModify = ref(false);
watch(
  [() => formParam.value],
  () => {
    isModify.value = true;
  },
  { deep: true },
);

// 清除表单
const cleanFormParam = () => {
  dailyTypeData.value = [];
  formParam.value = {
    year: undefined,
    deptName: dailyPersonUser?.value?.deptName,
    deptCode: dailyPersonUser?.value?.deptCode,
    items: [],
  };
  refreshDailyPersonUser();
};

// 监听值改变, 清除表单并重新加载
watch([() => type.value, () => id.value], async (n, o) => {
  cleanFormParam();
  if (
    ((n[0] as unknown as string) === 'edit' ||
      (n[0] as unknown as string) === 'adjust' ||
      (n[0] as unknown as string) === 'summarize' ||
      (n[0] as unknown as string) === 'evaluate' ||
      (n[0] as unknown as string) === 'planform-evaluate' ||
      (n[0] as unknown as string) === 'resetting') &&
    n[1]
  ) {
    dailyAnnualPlanDetailApiRun(n[1] as unknown as number);
  }
});

// 年度目标类型改变
const changeYear = () => {
  formParam.value.year = parseInt(year.value);
  dailyTypeParam.value.year = formParam.value.year;
  formParam?.value?.items?.forEach((it) => {
    it.typeId = undefined;
    it.typeName = undefined;
  });
  dailyTypeApiRun();
};
const disabledDate = (current: Dayjs) => {
  return current && (existYears.value as any)?.includes(parseInt(current.format('YYYY')));
};
const collapseActiveKey = ref([1, 2, 3, 4, 5, 6, 7, 8]);

const dailyTypeData = ref<IAnnualPlanTypeListResponse[]>();
const dailyTypeParam = ref<IAnnualPlanTypeListRequest>({
  state: AnnualPlanTypeStateConstant.VALID.code,
});
const dailyTypeApiRun = (year?: number) => {
  if (dailyTypeParam.value.year || year) {
    if (year) {
      dailyTypeParam.value.year = year;
    }
    dailyTypeApi.list(dailyTypeParam.value).then((it) => {
      dailyTypeData.value = it;
    });
  }
};
dailyTypeApiRun();

// 表单
const formRef = ref<FormInstance>({} as FormInstance);
const currentFormParamItems = computed(() => {
  return formParam.value.items?.filter((it) => {
    return !it.isDeleted;
  });
});
const addDomain = () => {
  formParam.value.items?.push({ monthPlans: [], planUnit: '万元' });
};
const removeDomain = (item: any) => {
  if (item.id) {
    item.isDeleted = true;
  } else {
    let index = formParam.value.items?.indexOf(item);
    if (index != undefined && index !== -1) {
      formParam.value.items?.splice(index, 1);
    }
  }
};

// 表单提交相关
const saveLoading = ref(false);
const submitLoading = ref(false);
const assembleRequest = () => {
  formParam.value.items?.forEach((it) => {
    if (it.planType === PlanTypeConstant.QUANTIFY.code) {
      it.planDesc === '';
    }
    if (it.planType === PlanTypeConstant.QUALITATIVE.code) {
      it.planValue === 0;
      it.planUnit === '';
    }
    it.monthPlans?.forEach((im) => {
      im.planType = it.planType;
      im.planType = it.planType;
      im.apTypeId = it.typeId;
      im.apTypeName = it.typeName;
      im.planUnit = it.planUnit;
      if (it.planType === PlanTypeConstant.QUANTIFY.code) {
        im.planDesc === '';
      }
      if (it.planType === PlanTypeConstant.QUALITATIVE.code) {
        im.planValue === 0;
        im.planUnit === '';
      }
    });
  });
};
const saveChange = () => {
  assembleRequest();
  saveLoading.value = true;
  if ((type.value as any) === 'add') {
    return dailyAnnualPlanApi
      .save(formParam.value)
      .then((it) => {
        message.success('新增年度计划成功!');
        cleanFormParam();
        dailyAnnualPlanDetailApiRun(it);
        setTimeout(() => {
          router.push({
            path: '/daily/annual-plan',
            query: { timestamp: routerParam(dayjs().toDate().getTime()), listType: 1 },
          });
        }, 1000);
      })
      .finally(() => {
        saveLoading.value = false;
      });
  } else if ((type.value as any) === 'edit') {
    return dailyAnnualPlanApi
      .update(formParam.value)
      .then((it) => {
        message.success('年度计划更新成功!');
        cleanFormParam();
        dailyAnnualPlanDetailApiRun(it);
        setTimeout(() => {
          router.push({
            path: '/daily/annual-plan',
            query: { timestamp: routerParam(dayjs().toDate().getTime()), listType: 1 },
          });
        }, 1000);
      })
      .finally(() => {
        saveLoading.value = false;
      });
  } 
};
const evaluateSubmitParam = ref<IEvaluateSaveRequestDTO>({});
const submitChange = () => {
  assembleRequest();
  saveLoading.value = true;
  if ((type.value as any) === 'add') {
    return dailyAnnualPlanApi
      .save(formParam.value)
      .then((it) => {
        submitLoading.value = true;
        dailyAnnualPlanApi
          .submit({ id: it })
          .then(() => {
            message.success('提交年度计划成功!');
            cleanFormParam();
            dailyAnnualPlanDetailApiRun(it);
            setTimeout(() => {
              router.push({
                path: '/daily/annual-plan',
                query: { timestamp: routerParam(dayjs().toDate().getTime()), listType: 1 },
              });
            }, 1000);
          })
          .finally(() => {
            submitLoading.value = false;
          });
      })
      .finally(() => {
        saveLoading.value = false;
      });
  } else if ((type.value as any) === 'edit') {
    return dailyAnnualPlanApi
      .update(formParam.value)
      .then((it) => {
        submitLoading.value = true;
        dailyAnnualPlanApi
          .submit({ id: it })
          .then(() => {
            message.success('提交年度计划成功!');
            cleanFormParam();
            dailyAnnualPlanDetailApiRun(it);
            setTimeout(() => {
              router.push({
                path: '/daily/annual-plan',
                query: { timestamp: routerParam(dayjs().toDate().getTime()), listType: 1 },
              });
            }, 1000);
          })
          .finally(() => {
            submitLoading.value = false;
          });
      })
      .finally(() => {
        saveLoading.value = false;
      });
  } else if ((type.value as any) === 'adjust') {
    return dailyAnnualPlanApi
      .add(formParam.value)
      .then((it) => {
        message.success('提交计划调整成功!');
        cleanFormParam();
        dailyAnnualPlanDetailApiRun(it);
        setTimeout(() => {
          router.push({
            path: '/daily/month-plan',
            query: { timestamp: routerParam(dayjs().toDate().getTime()), listType: 1 },
          });
        }, 1000);
      })
      .finally(() => {
        saveLoading.value = false;
      });
  } else if ((type.value as any) === 'summarize') {
    formParam.value.items?.forEach((it) => {
      (it as any).month = month.value;
      it.monthPlans?.forEach((itm) => {
        itm.completeRate = parseFloat(((itm.completePlanValue || 0) / (itm.planValue || 0)).toFixed(2));
      });
      it.monthPlans = it.monthPlans?.filter((itm) => {
        return parseInt(itm.month as any) === parseInt(month.value as any);
      });
    });
    return dailyAnnualPlanApi
      .updateClose(formParam.value)
      .then((it) => {
        message.success('提交计划总结成功!');
        cleanFormParam();
        dailyAnnualPlanDetailApiRun(it);
        setTimeout(() => {
          router.push({
            path: '/daily/month-plan',
            query: { timestamp: routerParam(dayjs().toDate().getTime()), listType: 1 },
          });
        }, 1000);
      })
      .finally(() => {
        saveLoading.value = false;
      });
  } else if ((type.value as any) === 'evaluate') {
    evaluateSubmitParam.value.id = formParam.value.id;
    evaluateSubmitParam.value.condition =
      evaluateSubmitParam.value.evaluates && evaluateSubmitParam.value.evaluates.length > 0 ? 1 : 0;
    const monthPlans = data.value?.monthPlans?.filter((it) => {
      return parseInt(it.month as any) === parseInt(month.value as any);
    });
    let monthPlan: IMonthPlanDetailResponseDTO = {};
    if (monthPlans && monthPlans.length > 0) {
      monthPlan = monthPlans[0];
    }
    // 当无具体评价内容时,可直接通过,需要构建一条空数据
    if (!evaluateSubmitParam.value.evaluates || (evaluateSubmitParam.value.evaluates.length < 0 && data)) {
      evaluateSubmitParam.value.evaluates = [{}];
    }
    evaluateSubmitParam.value.evaluates.forEach((it) => {
      it.apCode = data.value?.code;
      it.apVer = data.value?.ver;
      it.mpCode = monthPlan?.code;
      it.mpVer = monthPlan?.ver;
      it.state = EvaluateStateConstant.VALID.code;
    });
    saveLoading.value = true;
    evaluateApi
      .saveOrUpdateByManger(evaluateSubmitParam.value)
      .then(() => {
        message.success('计划评价成功!');
        cleanFormParam();
        dailyAnnualPlanDetailApiRun(data?.value?.id!!);
        setTimeout(() => {
          router.push({
            path: '/daily/platform/month-plan',
            query: { timestamp: routerParam(dayjs().toDate().getTime()), listType: 2 },
          });
        }, 1000);
      })
      .finally(() => {
        evaluateSubmitParam.value = {};
        saveLoading.value = false;
      });
  } else if ((type.value as any) === 'planform-evaluate') {
    evaluateSubmitParam.value.id = formParam.value.id;
    evaluateSubmitParam.value.condition =
      evaluateSubmitParam.value.evaluates && evaluateSubmitParam.value.evaluates.length > 0 ? 1 : 0;

    const monthPlans = data.value?.monthPlans?.filter((it) => {
      return parseInt(it.month as any) === parseInt(month.value as any);
    });
    let monthPlan: IMonthPlanDetailResponseDTO = {};
    if (monthPlans && monthPlans.length > 0) {
      monthPlan = monthPlans[0];
    }
    // 当无具体评价内容时,可直接通过,需要构建一条空数据
    if (!evaluateSubmitParam.value.evaluates || (evaluateSubmitParam.value.evaluates.length < 0 && data)) {
      evaluateSubmitParam.value.evaluates = [{}];
    }
    evaluateSubmitParam.value.evaluates.forEach((it) => {
      it.apCode = data.value?.code;
      it.apVer = data.value?.ver;
      it.mpCode = monthPlan?.code;
      it.mpVer = monthPlan?.ver;
      it.state = EvaluateStateConstant.VALID.code;
    });
    saveLoading.value = true;
    evaluateApi
      .saveOrUpdateByPlatform(evaluateSubmitParam.value)
      .then(() => {
        message.success('平台评价成功!');
        cleanFormParam();
        dailyAnnualPlanDetailApiRun(data?.value?.id!!);
        setTimeout(() => {
          router.push({
            path: '/daily/platform/month-plan',
            query: { timestamp: routerParam(dayjs().toDate().getTime()), listType: 2 },
          });
        }, 1000);
      })
      .finally(() => {
        evaluateSubmitParam.value = {};
        saveLoading.value = false;
      });
  } else if ((type.value as any) === 'resetting') {
    formParam.value.id = undefined;
    formParam.value.items = formParam.value.items
      ?.filter((it) => !it.isDeleted)
      ?.map((it) => {
        // it.id = undefined;
        it.monthPlans =  it.monthPlans
          ?.filter((itm) => !itm.isDeleted)
          ?.map((itm) => {
            // itm.id = undefined;
            return itm
          });
          return it
      });
    return dailyAnnualPlanApi
      .resetting(formParam.value)
      .then((it) => {
        submitLoading.value = true;
        dailyAnnualPlanApi
          .submit({ id: it })
          .then(() => {
            message.success('重制年度计划成功!');
            cleanFormParam();
            dailyAnnualPlanDetailApiRun(it);
            setTimeout(() => {
              router.push({
                path: '/daily/annual-plan',
                query: { timestamp: routerParam(dayjs().toDate().getTime()), listType: 1 },
              });
            }, 1000);
          })
          .finally(() => {
            submitLoading.value = false;
          });
      })
      .finally(() => {
        saveLoading.value = false;
      });
  }
};
const submitForm = () => {
  formRef.value.validate().then(() => {
    submitChange();
  });
};

const saveForm = () => {
  formRef.value.validate().then(() => {
    saveChange();
  });
};

const cancelForm = () => {
  router.go(-1);
};
window.onbeforeunload = function (e) {
  if (isModify.value) {
    e.returnValue = '确定离开当前页面吗？';
  }
};

const evaluateParam = computed(() => {
  const result = {
    micro: {
      evaluates: [],
    },
    strategy: {
      evaluates: [],
    },
    finance: {
      evaluates: [],
    },
    control: {
      evaluates: [],
    },
    manpower: {
      evaluates: [],
    },
    platform: {
      evaluates: [],
    },
  };
  data.value?.monthPlans?.forEach((it) => {
    if (parseInt((it as any).month) === parseInt(prop?.query?.month)) {
      it.evaluate?.forEach((ite) => {
        if (EvaluateTypeConstant.MICRO_MONTH.code === ite.type) {
          (result.micro.evaluates as any).push(ite);
        }
        if (EvaluateTypeConstant.THREE_STRATEGY.code === ite.type) {
          (result.strategy.evaluates as any).push(ite);
        }
        if (EvaluateTypeConstant.THREE_FINANCE.code === ite.type) {
          (result.finance.evaluates as any).push(ite);
        }
        if (EvaluateTypeConstant.THREE_CONTROL.code === ite.type) {
          (result.control.evaluates as any).push(ite);
        }
        if (EvaluateTypeConstant.THREE_MANPOWER.code === ite.type) {
          (result.manpower.evaluates as any).push(ite);
        }
        if (EvaluateTypeConstant.PLATFORM_MONTH.code === ite.type) {
          (result.platform.evaluates as any).push(ite);
        }
      });
    }
  });
  return result;
});

{
  // 初始化
  if (
    ((type.value as unknown as string) === 'edit' ||
      (type.value as unknown as string) === 'adjust' ||
      (type.value as unknown as string) === 'summarize' ||
      (type.value as unknown as string) === 'evaluate' ||
      (type.value as unknown as string) === 'planform-evaluate' ||
      (type.value as unknown as string) === 'resetting') &&
    id.value
  ) {
    dailyAnnualPlanDetailApiRun(id.value as unknown as number);
  }
}
const getIndex = (record: any) => {
  return formParam.value.items?.indexOf(record) || 0;
};
const year = ref<string>('');
</script>

<template>
  <div style="background-color: #ffff; height: 100%; width: 100%; padding: 10px 10px 0px 10px; overflow: auto">
    <div
      v-if="saveLoading || loadLoading"
      style="
        text-align: center;
        height: 100vh;
        width: 90vw;
        position: fixed;
        z-index: 100;
        opacity: 0.5;
        background-color: #ffff;
        padding-top: 35vh;
      "
    >
      <h-spin :size="'large'" />
    </div>
    <div
      v-if="saveLoading || submitLoading || loadLoading"
      style="
        text-align: center;
        height: 100vh;
        width: 90vw;
        position: fixed;
        z-index: 100;
        opacity: 0.5;
        background-color: #ffff;
        padding-top: 35vh;
      "
    >
      <h-spin :size="'large'" />
    </div>
    <h-form ref="formRef" name="dynamic_form_item" :model="formParam" :scrollToFirstError="true">
      <h-row :align="'middle'" style="margin: 12px 0">
        <h-col :offset="1" :span="4">
          <h-form-item
            label="选择年份"
            :name="['year']"
            :rules="{
              required: true,
              message: '请选择目标类型!',
            }"
          >
            <h-date-picker
              :disabled="(type as any) === 'edit' || 
              (type as any) === 'adjust' || 
              (type as any) === 'summarize' ||
              (type as any) === 'evaluate' ||
              (type as any) === 'planform-evaluate' ||
              (type as any) === 'resetting'"
              v-model:value="year"
              :disabled-date="(disabledDate as any)"
              value-format="YYYY"
              picker="year"
              @change="changeYear"
            />
          </h-form-item>
        </h-col>
        <h-col :span="14" style="text-align: center">
          <div style="font-size: 20px; font-weight: 700">
            {{ formParam?.year || '----' }} 年
            <template v-if="month !== undefined && month !== 'undefined'"> {{ month }} 月 </template>
            - {{ formParam?.deptName || '------' }} - 小微目标
          </div>
        </h-col>
      </h-row>
      <h-row :align="'middle'">
        <h-col :span="24">
          <h-collapse v-model:activeKey="collapseActiveKey" :bordered="false" style="background-color: white">
            <h-collapse-panel key="1">
              <template #header>
                <div style="display: flex">
                  <div style="height: 25px; width: 10px; background-color: rgb(0, 115, 229)" />
                  <div style="font-size: 16px; font-weight: 600; margin-left: 10px">年度目标维护</div>
                </div>
              </template>
              <div style="border-top: 0.5px solid rgb(217, 217, 217); width: 100%; margin-bottom: 20px"></div>
              <update-year-target :type="type" :form-param="formParam" style="margin-bottom: 20px"></update-year-target>
              <update-year-plan
                v-for="(i, index) of currentFormParamItems"
                :key="i?.id"
                :form-param="formParam"
                :current="i"
                :current-index="getIndex(i)"
                :daily-type="dailyTypeData"
                :type="type"
                :month="month"
                @delete="removeDomain"
              ></update-year-plan>
              <h-col
                :span="24"
                style="margin: 5px 24px"
                v-if="type !== 'adjust' && type !== 'summarize' && type !== 'evaluate' && type !== 'planform-evaluate'"
              >
                <h-form-item>
                  <h-button type="dashed" style="width: 100%" @click="addDomain">
                    <PlusOutlined />
                    添加年度项目
                  </h-button>
                </h-form-item>
              </h-col>
            </h-collapse-panel>
            <h-collapse-panel v-if="type === 'evaluate'" key="2">
              <template #header>
                <div style="display: flex">
                  <div style="height: 25px; width: 10px; background-color: rgb(0, 115, 229)" />
                  <div style="font-size: 16px; font-weight: 600; margin-left: 10px">平台{{ evaluateTypeName }}</div>
                </div>
              </template>
              <div style="border-top: 0.5px solid rgb(217, 217, 217); width: 100%; margin-bottom: 20px"></div>
              <evaluate
                v-if="evaluateType"
                :type="type"
                :evaluate-type="evaluateType"
                :evaluate-group-flag="evaluateGroupFlag"
                :save-param="evaluateSubmitParam"
                style="margin-bottom: 20px"
              />
            </h-collapse-panel>
            <h-collapse-panel
              v-if="type === 'planform-evaluate' && evaluateParam.strategy.evaluates.length > 0"
              key="3"
            >
              <template #header>
                <div style="display: flex">
                  <div style="height: 25px; width: 10px; background-color: rgb(0, 115, 229)" />
                  <div style="font-size: 16px; font-weight: 600; margin-left: 10px">平台三自评价-战略</div>
                </div>
              </template>
              <div style="border-top: 0.5px solid rgb(217, 217, 217); width: 100%; margin-bottom: 20px"></div>
              <evaluate
                v-if="data"
                :show="1"
                :save-param="evaluateParam.strategy"
                style="margin-bottom: 20px"
              ></evaluate>
            </h-collapse-panel>
            <h-collapse-panel v-if="type === 'planform-evaluate' && evaluateParam.control.evaluates.length > 0" key="4">
              <template #header>
                <div style="display: flex">
                  <div style="height: 25px; width: 10px; background-color: rgb(0, 115, 229)" />
                  <div style="font-size: 16px; font-weight: 600; margin-left: 10px">平台三自评价-风控</div>
                </div>
              </template>
              <div style="border-top: 0.5px solid rgb(217, 217, 217); width: 100%; margin-bottom: 20px"></div>
              <evaluate
                v-if="data"
                :show="1"
                :save-param="evaluateParam.control"
                style="margin-bottom: 20px"
              ></evaluate>
            </h-collapse-panel>
            <h-collapse-panel v-if="type === 'planform-evaluate' && evaluateParam.finance.evaluates.length > 0" key="5">
              <template #header>
                <div style="display: flex">
                  <div style="height: 25px; width: 10px; background-color: rgb(0, 115, 229)" />
                  <div style="font-size: 16px; font-weight: 600; margin-left: 10px">平台三自评价-财务</div>
                </div>
              </template>
              <div style="border-top: 0.5px solid rgb(217, 217, 217); width: 100%; margin-bottom: 20px"></div>
              <evaluate
                v-if="data"
                :show="1"
                :save-param="evaluateParam.finance"
                style="margin-bottom: 20px"
              ></evaluate>
            </h-collapse-panel>
            <h-collapse-panel
              v-if="type === 'planform-evaluate' && evaluateParam.manpower.evaluates.length > 0"
              key="6"
            >
              <template #header>
                <div style="display: flex">
                  <div style="height: 25px; width: 10px; background-color: rgb(0, 115, 229)" />
                  <div style="font-size: 16px; font-weight: 600; margin-left: 10px">平台三自评价-人力</div>
                </div>
              </template>
              <div style="border-top: 0.5px solid rgb(217, 217, 217); width: 100%; margin-bottom: 20px"></div>
              <evaluate
                v-if="data"
                :show="1"
                :save-param="evaluateParam.manpower"
                style="margin-bottom: 20px"
              ></evaluate>
            </h-collapse-panel>
            <h-collapse-panel v-if="type === 'planform-evaluate'" key="7">
              <template #header>
                <div style="display: flex">
                  <div style="height: 25px; width: 10px; background-color: rgb(0, 115, 229)" />
                  <div style="font-size: 16px; font-weight: 600; margin-left: 10px">平台评价</div>
                </div>
              </template>
              <div style="border-top: 0.5px solid rgb(217, 217, 217); width: 100%; margin-bottom: 20px"></div>
              <evaluate
                v-if="evaluateType"
                :type="type"
                :evaluate-type="evaluateType"
                :evaluate-group-flag="evaluateGroupFlag"
                :save-param="evaluateSubmitParam"
                style="margin-bottom: 20px"
              />
            </h-collapse-panel>
          </h-collapse>
        </h-col>
      </h-row>
      <h-row :align="'middle'">
        <h-col v-if="type === 'add' || type === 'edit'" :span="2" offset="8" style="text-align: center">
          <h-form-item>
            <h-button type="primary" style="width: 100%" @click="saveForm"> 保存 </h-button>
          </h-form-item>
        </h-col>
        <h-col v-else :span="1" offset="8" style="text-align: center" />
        <h-col :span="2" offset="1" style="text-align: center">
          <h-form-item>
            <h-popconfirm :title="submitTitle" ok-text="确认" cancel-text="取消" @confirm="submitForm">
              <h-button type="primary" style="width: 100%"> 提交 </h-button>
            </h-popconfirm>
          </h-form-item>
        </h-col>
        <h-col :span="2" offset="1" style="text-align: center">
          <h-form-item>
            <h-button style="width: 100%" @click="cancelForm"> 取消 </h-button>
          </h-form-item>
        </h-col>
      </h-row>
    </h-form>
  </div>
</template>

<style scoped lang="less">
@import '../../../assets/css/main.less';
</style>
