<template>
  <div class="bg-eee">

    <swiper class="swiper my-swiper" @setTranslate="setTranslate" @touchEnd="touchEnd">
      <swiper-slide v-for="img,index in detail?.album" :key="index" class="slide">
        <van-image class="mb-5" width="100%" style="height: 100%" fit="cover"
          :src="`${businessList}api/common/v1/file/download/${img.id}`" />
        <div v-if="index == detail?.album?.length -1" class="show-more">
          <div class="text">
            {{ isSwiperMore ? '释放查看更多' : '左滑查看更多' }}
          </div>
        </div>
      </swiper-slide>

    </swiper>


    <div class="detail-title-box">
      <div class="flex mb-12">
        <div style="max-width: 75%;" class="detail-title">{{ detail?.fullname }}</div>
        <div class="detail-star flex">
          <img v-if="detail?.starLevel >= 50" src="../../../assets/image/restaurant/star5.png" alt="" />
          <img v-else src="../../../assets/image/restaurant/star4.png" alt="" />
          <div :class="detail?.starLevel >= 50 ? 'star-level-5' : ''">{{ numToStarStr(detail?.starLevel) }}</div>
        </div>
      </div>

      <div class="detail-food mb-10">
        <!-- {{ getFoodTypeStr(detail?.cateType || []) }} -->
        <div class="food">{{dayjs(detail?.decorateDate).format('YYYY年MM月')}}装修</div>
        <div class="detail-area">
          <span class="tag tag-detail" v-for="(mark, markI) in detail?.landMark" :key="markI">
            {{ mark.name }}
          </span>
        </div>
      </div>

      
      <van-row v-if=" detail?.longitude && detail?.latitude">
        <van-col :span="24">
          <a-map ref="map"> </a-map>
        </van-col>
      </van-row>
    </div>

    <div class="">

    </div>

    <div class="container">
      <div class="detail-title-time mb-20 flex align-items-center" >
        <div class="search-time-start flex" @click="openTimePicker('begin')">
          <div class="search-time-text">
            <span>{{ dayjs(checkIn).month() + 1 }}</span>月<span>{{ dayjs(checkIn).date() }}</span>日
          </div>
          <div class="search-time-text-small ml-5">{{ timeToStr(checkIn) }}</div>
        </div>
        
        <van-tag  class="time-range-tag" type="primary">共{{dayjs(checkOut).diff(checkIn, 'day')}}晚</van-tag>

        <div class="search-time-start flex" @click="openTimePicker('end')">
          <div class="search-time-text">
            <span>{{ dayjs(checkOut).month() + 1 }}</span>月<span>{{ dayjs(checkOut).date() }}</span>日
          </div>
          <div class="search-time-text-small ml-5">{{ timeToStr(checkOut) }}</div>
        </div>
      </div>
      <div class="items-rooms-list mb-10">
        <roomList :check-in="checkIn" :check-out="checkOut" :roomList="detail?.roomList"></roomList>
      </div>
    </div>

    <div class="detail-huanjing-box">
      <div class="detail-title-s mb-10">
        酒店环境
        <span class="title-more" @click="goToImgList('room')">更多</span>
      </div>

      <div class="scroll-hidden">
        <div class="scroll-body" v-if="detail?.album.length > 0">
          <div class="scroll-secbody">
            <van-image v-for="(item, index) in detail?.album" :key="index" class="mr-10 every_content" width="100px"
              height="100px" fit="cover" position="left" @click="showRoomPreview(index)"
              :src="`${businessList}api/common/v1/file/download/${item.id}`">
              <div class="img-title">
                <van-text-ellipsis :content="item.title" />
              </div>
            </van-image>
          </div>

        </div>
        <div v-else class="empty-box">
          <van-empty :image-size="[100, 60]" image="search" description="暂无数据" />
        </div>
      </div>
    </div>

    <div class="detail-desc-box mt-20">

      <div class="detail-title-s mb-10">酒店简介</div>

      <!-- vant 4.8.4以上才能使用 -->
      <van-text-ellipsis class="show-more-btn" rows="3" :content="detail?.description" expand-text="展开更多"
        collapse-text="收起更多">
        <template #action="{ expanded }">
          <span class="mr-5">{{ expanded ? '收起更多' : '展开更多' }}</span>
          <van-icon :name="expanded ? 'arrow-up' : 'arrow-down'" />
        </template>

      </van-text-ellipsis>


    </div>

    <div class="detail-more-box mt-20">
      <div class="detail-title-s mb-10">酒店更多信息</div>

      <van-row justify="space-between" class="mb-16">
        <van-col span="6" class="font-size-28"> 电话: </van-col>
        <van-col span="18" class="font-size-28 detail-text-color">
          {{ detail?.contactMobile || detail?.contactPhone }}
        </van-col>
      </van-row>

      <!-- <van-row justify="space-between" class=" mb-10">
        <van-col span="6" class="font-size-12"> 营业时间: </van-col>
        <van-col span="18" class="font-size-12 detail-text-color">
          {{ detail?.businessTime }}
        </van-col>
      </van-row> -->
      <van-row justify="space-between" class="mb-16">
        <van-col span="6" class="font-size-28"> 装修时间: </van-col>
        <van-col span="18" class="font-size-28 detail-text-color">
          {{ detail?.decorateDate }}
        </van-col>
      </van-row>
      <van-row justify="space-between" class="mb-16">
        <van-col span="6" class="font-size-28"> 楼层: </van-col>
        <van-col span="18" class="font-size-28 detail-text-color"> {{ detail?.floors }}层 </van-col>
      </van-row>
      <!-- <van-row justify="space-between" class="mb-16">
        <van-col span="6" class="font-size-12"> 菜系: </van-col>
        <van-col span="18" class="font-size-12 detail-text-color">
          {{ detail?.cateClass.map((item:any) => item.name).join(',') }}
        </van-col>
      </van-row> -->
      <van-row justify="space-between" class="mb-16">
        <van-col span="6" class="font-size-28"> 特色服务: </van-col>
        <van-col span="18" class="font-size-28 detail-text-color">
          {{ detail?.service }}
        </van-col>
      </van-row>
      <van-row justify="space-between" class="mb-16">
        <van-col span="6" class="font-size-28"> 周边环境: </van-col>
        <van-col span="18" class="font-size-28 detail-text-color">
          {{ detail?.environment }}
        </van-col>
      </van-row>

      <van-row justify="space-between" class="mb-16">
        <van-col span="6" class="font-size-28"> 公交: </van-col>
        <van-col span="18" class="font-size-28 detail-text-color">
          {{ detail?.busLine }}
        </van-col>
      </van-row>

      <van-row justify="space-between" class="mb-16">
        <van-col span="6" class="font-size-28"> 机场距离: </van-col>
        <van-col span="18" class="font-size-28 detail-text-color"> {{ detail?.airportDistance }}km </van-col>
      </van-row>

      <van-row justify="space-between" class="mb-16">
        <van-col span="6" class="font-size-28"> 火车站距离: </van-col>
        <van-col span="18" class="font-size-28 detail-text-color"> {{ detail?.railwayDistance }}km </van-col>
      </van-row>

      <van-row justify="space-between">
        <van-col span="6" class="font-size-28"> 停车: </van-col>
        <van-col span="18" class="font-size-28 detail-text-color">
          {{ detail?.stop }}
        </van-col>
      </van-row>
    </div>

    <!-- 订餐折扣政策 -->
    <van-dialog v-model:show="discountDialog" :close-on-click-overlay="true" title="海尔订餐须知">
      <div style="padding: 20px; height: 400px; overflow-y: auto;" @scroll="handleScroll">
        <p style="font-size: 12px; ">结合我集团纪委颁布的《关于狠刹公款吃喝严禁铺张浪费的通知》，为推进集团商务用餐市场效果落地，实现自挣自花，现对商务订餐系统的审批流程升级：</p>
        <div style="font-size: 12px; font-weight: bold;">
          <br />一、订餐预订条件：
          <br />
        </div>
        <div style="font-size: 10px;">
          1.必须有月度可用预算；
          <br />2.必须明确申请事由、业务目标、市场效果预算，确认招待必要性；
          <br />3.必须符合集团宴请标准，不超标，不随意提高标准，不随意增加人数，超预算部分费用自付；
          <br />4.必须系统中审批通过；
          <br />5.符合自挣自花原则；
          <br />6.首选集团内酒店资源
        </div>

        <div style="font-size: 12px; font-weight: bold;">
          <br />二、 直线经理、二线经理项目及责任：
          <br />
        </div>
        <div style="font-size: 10px;">
          1.审核业务的市场效果及目标；
          <br />2.审核业务的标准是否超标；
          <br />3.审核业务的必要性、真实性、合规性；
          <br />4.如有违规行为一经查出或经举报查实的,全部损失由责任人承担，同时参照《员工行为规范》一级违规处理。
        </div>

        <div style="font-size: 12px; font-weight: bold;">
          <br />三、宴请招待预算控制标准：
          <br />
        </div>
        <table border="1" cellpadding="0" cellspacing="0" class="tb"
          style="table-layout: fixed; font-size: 8px; text-align: center; line-height: 22px;">
          <tbody>
            <tr>
              <td width="20">序号</td>
              <td width="60">来宾级别</td>
              <td width="50">参加领导</td>
              <td width="50">菜金费用标准</td>
              <td width="100">地点选择</td>
              <td width="300">备注</td>
            </tr>
            <tr>
              <td>1</td>
              <td>副省级以上/著名专家学者</td>
              <td>D-A级（副总裁以上）</td>
              <td>根据来宾按需求确定</td>
              <td>可按来宾要求自行确定或选择协议酒店</td>
              <td rowspan="4" style="text-align: left">
                1、业务宴请订单必须根据菜金标准、酒水标准等确定费用总额上限；
                <br />2、用餐总费用不得超过预算总额，超标部分由宴请人自行现付买单；
                <br />3、酒水要求：建议业务宴请使用的酒水（除啤酒外），统一从创牌商务餐厅采购。电话：88936033；
                <br />4、如需为司机提取工作餐，必须在预定时备注预提工作餐客人名单，标准为50元/位；
                <br />5、陪同人员不超客人的三分之一；
                <br />6、行为规范要求：个人在用餐过程中必须以海尔人的标准规范自己行为，维护集团形象，因个人行为不当造成不良影响按照一级违规处理；
                <br />7、投入产出要求：商务宴请需要有投入产出分析，必须以实际就餐人数提交预算，不得私自变更人数或提高预算；
                <br />8、业务合规性要求：宴请业务需求必须与业务目标相同，并保证宴请效果。
                <br />本次餐标调整从2015年12月01日开始执行。
              </td>
            </tr>
            <tr>
              <td>2</td>
              <td>正厅级、局级/中央省市新闻媒体（大单及以上）</td>
              <td>10-11级</td>
              <td>不得超过260元/位上限</td>
              <td>选择协议内酒店资源</td>
            </tr>
            <tr>
              <td>3</td>
              <td>副局、处长以上（中大单及大单以上）</td>
              <td>8-10级</td>
              <td>不得超过200元/位上限</td>
              <td>选择协议内酒店资源</td>
            </tr>
            <tr>
              <td>4</td>
              <td>一般公务人员（中单或中小单以上）</td>
              <td>7级~10级</td>
              <td>不得超过150元/位上限</td>
              <td>选择协议内酒店资源</td>
            </tr>
          </tbody>
        </table>

        <div style="font-size: 12px; font-weight: bold;">
          <br />四、关于相关接口人责任：
          <br />
        </div>
        <table border="1" cellspacing="0" cellpadding="5" style="text-align: center; font-size: 8px;">
          <tbody>
            <tr>
              <td width="52">序号</td>
              <td width="170">角色</td>
              <td width="570">责任承诺</td>
            </tr>
            <tr>
              <td>1</td>
              <td>业务申请人</td>
              <td align="left">
                1.对业务必要性、真实性、合规性负责；
                <br />2.对符合集团政策, 不超标，不随意提高标准，不随意增加人数负责；
                <br />3.对陪餐人数在原则内不超过客人人数的三分之一负责；
                <br />4.对超预算部分费用自付；
                <br />5.自挣自花负责；
                <br />6.如有违规行为一经查出或经举报查实的,全部损失由责任人承担，同时参照《员工行为规范》一级违规处理。
              </td>
            </tr>
            <tr>
              <td>2</td>
              <td>业务闸口人及审批人</td>
              <td align="left">
                1.对业务必要性、真实性、合规性负责；
                <br />2.对符合集团政策, 不超标，不随意提高标准，不随意增加人数负责；
                <br />3.对陪餐人数在原则内不超过客人人数的三分之一负责人；
                <br />4.对超预算部分费用自付；
                <br />5.对自挣自花负责，确认招待必要性；
                <br />6.如有违规行为一经查出或经举报查实的,全部损失由责任人承担，同时参照《员工行为规范》一级违规处理。
              </td>
            </tr>
            <tr>
              <td>3</td>
              <td>签单人</td>
              <td align="left">
                1.对账单准确性、真实性负责；
                <br />2.对违规提取工作餐负责；
                <br />3.对超预算部分费用自付；
                <br />4.如有违规行为一经查出或经举报查实的,全部损失由责任人承担，同时参照《员工行为规范》一级违规处理。
              </td>
            </tr>
          </tbody>
        </table>

        <div style="font-size: 12px; font-weight: bold;">五、2024年青岛市协议酒店折扣政策：</div>
        <table border="1" cellspacing="0" cellpadding="5" style="text-align: center; font-size: 8px;">
          <tbody>
            <tr>
              <td rowspan="2">区域</td>
              <td rowspan="2">酒店名称</td>
              <td colspan="3">资源政策</td>
            </tr>
            <tr>
              <td>服务费</td>
              <td>中餐折扣</td>
              <td>自助餐价格</td>
            </tr>
            <tr v-for="(item,index) in hotelsData" :key="index">
              <td style="width:100px;">{{item.regionName}}</td>
              <td style="width:200px;">{{item.fullname}}</td>
              <td style="width:100px;">{{item.serviceFee}}%</td>
              <td style="width:300px;">{{item.discountPolicy ||'-'}}</td>
              <td style="width:100px;">{{item.buffetPrices ? item.buffetPrices + '元/人':'-' }}</td>
            </tr>
          </tbody>
        </table>


      </div>
      <template #footer>
        <van-row class="width100 " style="padding: 10px 0;" justify="center">

          <van-button size="small" type="primary" @click="goBooking">
            <span>已阅读订餐须知</span>
            <!-- <van-count-down v-if="!timeFinish" style=" display: inline-block;margin-left: 10px;color: #fff;" @finish="timeFinish = true" :time="5000" format="(ss)" /> -->
          </van-button>
        </van-row>
      </template>
    </van-dialog>

    <!-- 温馨提示 -->
    <!-- <van-dialog v-model:show="showInfo" :close-on-click-overlay="true" title="温馨提示" >
      <div style="padding: 20px; height: 200px;">
        <div>请各位员工必须再周一至周五下午5:30之前提交订单并审批通过,节假日请提前一个工作日完成</div>
        <div style="margin-top: 20px;">谢谢合作!</div>
      </div>
      <template #footer>
        <van-row class="width100 " style="padding: 10px 0;" justify="center" >
          
          <van-button  size="small"  type="primary"  @click="showInfo = false" >
            <span>我知道了</span>
          </van-button>
        </van-row>
      </template>
    </van-dialog> -->
     <!-- 入住日期选择 -->
    <van-popup v-model:show="showRqPicker" round position="bottom">
      <van-date-picker v-model="currentRq" title="选择日期" :min-date="minDate" :max-date="maxDate" @confirm="confirmRq"
        @cancel="showRqPicker = false" />
    </van-popup>

    <van-sticky position="bottom">
      <div class="bottom-order-btn">
        <van-button :loading="btnLoading" class="btn mr-20"
          @click="goToReservation('private')">因私预订</van-button>
        <!-- <van-button class="btn " type="primary" @click="goToReservation('public')">因公预订</van-button> -->
        <van-button class="btn btn-background " @click="handleBook">因公预订</van-button>
      </div>
    </van-sticky>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from 'vue';
import type { Ref } from 'vue';
import aMap from '@/components/aMap/index.vue';
import { showDialog } from 'vant';

import { localHotelApi } from '@haierbusiness-front/apis';
import roomList from './roomList.vue';

import { RHotelParams, RHotel, RCate } from '@haierbusiness-front/common-libs';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import { Item } from 'ant-design-vue/es/menu';
import dayjs from 'dayjs';

import { Swiper, SwiperSlide } from 'swiper/vue';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/scrollbar';
import 'swiper/css/autoplay';
import { showImagePreview } from 'vant';
import { showToast } from 'vant';


const router = getCurrentRouter();

const route = ref(getCurrentRoute());

const hotelId = route.value?.query?.hotelId;
const checkIn = ref(route.value?.query?.checkIn);
const checkOut = ref(route.value?.query?.checkOut);


const businessList = import.meta.env.VITE_BUSINESSTRAVEL_URL;

// 轮播图相关
const imgList = [
  "https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg",
  "https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg",

  "https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg",

  "https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg",

]

const isSwiperMore = ref(false)
// const showInfo = ref(true)

const minDate = ref((dayjs()).toDate());
const maxDate = ref(dayjs().add(1, 'year').toDate());
const choseTimeType = ref('')
const currentRq = ref<Array<string>>([]);

const showRqPicker = ref<boolean>(false);
const confirmRq = ({ selectedValues }: any) => {
  if (choseTimeType.value == 'begin') {
    checkIn.value = selectedValues.join('-');
  } else {
    checkOut.value = selectedValues.join('-');
  }
  showRqPicker.value = false;
};

  // 申请单的时间需要加限制，开始和结束日期区间最多是10天
  const openTimePicker = (type: string) => {
  choseTimeType.value = type;
  currentRq.value = [];
  minDate.value = dayjs().toDate();
  maxDate.value = dayjs().add(1, 'year').toDate();


  if (type == 'begin') {
    if (checkIn.value) {
      if (dayjs().isBefore(dayjs(checkIn.value))) {
        currentRq.value = checkIn.value?.split('-');
      } else {
        currentRq.value = []
      }
    }
    if (checkOut.value) {
      maxDate.value = (dayjs(checkOut.value).subtract(1, 'day')).toDate();
      // 最早只能选今天
      if (dayjs().isAfter(dayjs(minDate.value))) {
        minDate.value = (dayjs()).toDate();
      }
    }

  } else {
    if (checkOut.value) {
      if (dayjs().isBefore(dayjs(checkOut.value))) {
        currentRq.value = checkOut.value?.split('-');
      } else {
        currentRq.value = []
      }
    }
    if (checkIn.value) {
      minDate.value = (dayjs(checkIn.value)).add(1,'day').toDate();
    }
  }
  showRqPicker.value = true;

};

const setTranslate = (e, length:number) => {
  const len =  (e.slidesGrid[e.slidesGrid.length -1] + 80) * -1
  if (length < len) {
    isSwiperMore.value = true
  }
}

const showRoomPreview = (index: number) => {
  showImagePreview({
    images: detail.value.album.map((item:any) => `${businessList}api/common/v1/file/download/${item.photoId || item.id }`),
    startPosition: index,
  });
}

const showDishPreview = (index: number) => {
  showImagePreview({
    images: detail.value.dish.map((item:any) => `${businessList}api/common/v1/file/download/${item.photoId || item.id }`),
    startPosition: index,
  });
}

const touchEnd = (e, length) => {
  if (!isSwiperMore.value) {
    return
  }
  goToImgList('room')
}

// 选择的时间跟现在时间对比
const timeToStr = (time:string) => {
  const now = dayjs().format('YYYY-MM-DD');
  const days = dayjs(time).diff(now, 'day') // 7
  let timeStr = ''
  switch (days) {
    case 0:
      timeStr = '今天'
    break;
    case 1:
      timeStr = '明天'
    break;
    case 2:
      timeStr = '后天'
    break;
    default:
      timeStr = `${days}天后`
  }
  return timeStr
}

const goToImgList = (type: string) => {
  router.push({ path: '/hotel/hotel/imgList', query: { hotelId: hotelId, type } });

}


const pImg = new URL('@/assets/image/restaurant/pImg.png', import.meta.url).href;

const iconP = new URL('@/assets/image/restaurant/icon-position.png', import.meta.url).href;

const map = ref();

const goBack = () => {
  router.back(-1);
};

const description = ref()
const isShowMore = ref<boolean>(false)



// 查询订餐折扣政策
const dataList = () => {
  const params = {}
  localHotelApi.dataList(params).then(res => {
    hotelsData.value = res.data
  })
}

const hotelsData = ref([])
const isBottom = ref<boolean>(false)
const timeFinish = ref<boolean>(false)
const orderType= ref('')
const handleScroll = (event) => {
  let { scrollTop, scrollHeight, clientHeight } = event.target;
  if(isBottom.value) return
  isBottom.value = (scrollHeight - scrollTop) - 20 <= clientHeight;
}

const handleBook = () => {

  showDialog({
    message: '功能建设中',
  }).then(() => {
    // on close
  });
}
const btnLoading = ref(false)
const goToReservation = (val: string) => {
  const selectedRooms = detail.value.roomList.filter(item => item.select)

  if (selectedRooms.length < 1) {
    showToast('请先选择房型')
    return
  }
  orderType.value = val
  localStorage.setItem('roomList', JSON.stringify(detail.value.roomList))
  console.log(detail.value)
  goBooking(detail.value.id)
};

const goBooking = (id:number|string) => {
  router.push({ path: '/hotel/book', query: { type: orderType.value , hotelId: id, checkIn: checkIn.value, checkOut: checkOut.value } });
}


const detail = ref<any>();

const album = ref<any>({
  room: [],
  restaurant: [],
  food: [],
});

const getDetail = () => {
  const params = {
    hotelId: hotelId,
    checkIn: checkIn.value,
    checkOut: checkOut.value,
  };

  localHotelApi.hotelSingle(params).then((res) => {
    res.data?.roomList?.forEach((item:any) => {
      item.select = false
    });
    detail.value = res.data;

    // var type: Short? = null  //0：普通图片； 1：大堂；  2： 外景； 3： 餐厅； 4： 客房
    detail.value?.album?.forEach((item:any) => {
      switch (item.type) {
        case 0:
          album.value.food.push(item);
          break;
        case 1:
          album.value.restaurant.push(item);
          break;
        case 2:
          album.value.restaurant.push(item);
          break;
        case 3:
          album.value.room.push(item);
          break;
        case 4:
          album.value.room.push(item);
          break;

        default:
          break;
      }
    });

    // 飞到定位
    setTimeout(() => {
      if (map.value) {
        map.value.moveMapCenter([detail.value.longitude, detail.value.latitude], false, 1000);
        map.value.addMark(iconP, [detail.value.longitude, detail.value.latitude], detail.value.address);
      }
    }, 1000);
    
  });
};
const discountDialog= ref<boolean>(false);
const showMoreDescription = ref<boolean>(false);

const hotalStarList = [
  { text: '白金五星级', value: 55 },
  { text: '五星级', value: 50 },
  { text: '准五星', value: 45 },
  { text: '四星级', value: 40 },
  { text: '准四星', value: 35 },

  { text: '三星级', value: 30 },
  { text: '准三星', value: 25 },
  { text: '二星级', value: 20 },
  { text: '准二星', value: 15 },

  { text: '一星级', value: 10 },
  { text: '准一星', value: 5 },
];

const numToStarStr = (num: number) => {
  if (!num) {
    return '暂无星级';
  }
  let str = ''
  hotalStarList.forEach(item => {
    if (item.value == num) {
      str = item.text
    }
  })
  return str
};

watch(
  () => hotelId,
  () => {
    getDetail();
  },
);

watch(
  () => checkIn.value,
  () => {
    getDetail();
  },
);

watch(
  () => checkOut.value,
  () => {
    getDetail();
  },
);

onMounted(() => {
  getDetail();
  dataList()
});
</script>

<style lang='less' scoped>
@import url(./common.less);
.my-swiper {
  height: 420px;
  background: #eee;
  z-index: 0;
  .slide {
    width: 100%;
    height: 100%;
  }
}
.show-more {
  position: absolute;
    width: 100%;
    height: 100%;
    left: 100%;
    top: 0;
    z-index: 100;
    display: flex;
    align-items: center;
    .text {
      width: 10px;
      margin-left: 10px;
      color: #9d9b9b;
    }
}
.empty-box {
  height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.show-more-btn {
  :deep(.van-text-ellipsis__action) {
    display: flex;
    align-items: center;
    width: 100%;
    text-align: center;
    justify-content: center;
    margin-top: 5px;
  }
}
</style>