<script lang="ts" setup>
import {
  Select as hSelect, SelectOption as hSelectOption, Modal as hModal, Form as hForm, FormItem as hFormItem,
  Input as hInput, Textarea as hTextarea, <PERSON><PERSON> as hButton, DatePicker as hDatePicker, Col as hCol, Row as hRow, Upload as hUpload
} from 'ant-design-vue';
import { computed, ref, watch } from "vue";
import type { Ref } from "vue";
import {
  IInformationType
} from '@haierbusiness-front/common-libs';
import type { Rule } from "ant-design-vue/es/form";
import type { UploadFile, UploadChangeParam, UploadProps } from 'ant-design-vue'
import { message } from 'ant-design-vue'
import { UploadOutlined } from '@ant-design/icons-vue'
import PlusOutlined from '@ant-design/icons-vue/PlusOutlined';
import LoadingOutlined from '@ant-design/icons-vue/LoadingOutlined';
interface Props {
  show: boolean;
  data: IInformationType | null;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
});

const from = ref();
const confirmLoading = ref(false);

const defaultData: IInformationType = {
  id: null,
  creator: '',
  createTime: '',
  showStatus: 1,
  imgUrl: '',
  infoAuthor: '',
  infoDate: '',
  infoTitle: '',
  jumpLinkPc: '',
  jumpLinkApp: ''
};

const checkDisUrlTip = (url: string) => {
  const reg = /^((https|http|ftp|rtsp|mms)?:\/\/)[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]/;
  if (reg.test(url)) {
    return true;
  } else {
    return false;
  }
}

const validateUrl = (_rule: Rule, value: string, name: string) => {
  if (value !== "") {
    if (checkDisUrlTip(value)) {
      return Promise.resolve();
    } else {
      return Promise.reject('必须以http或https开头的网页链接！');
    }
  } else {
    return Promise.reject(`请填写${name}！`);
  }
}

const rules = {
  imgUrl: [
    { required: true, message: '请上传应用图标！' }
  ],
  infoAuthor: [
    { required: true, message: '请填写资讯作者！' }
  ],
  infoDate: [
    { required: true, message: '请填写资讯发布日期！' }
  ],
  infoTitle: [
    { required: true, message: '请填写资讯标题！' }
  ],
  jumpLinkApp: [
    { required: true, validator: (_rule: Rule, value: string) => validateUrl(_rule, value, '移动端链接'), trigger: 'change' }
  ],
  jumpLinkPc: [
    { required: true, validator: (_rule: Rule, value: string) => validateUrl(_rule, value, 'PC端链接'), trigger: 'change' }
  ],
  showStatus: [
    { required: true, message: '请选择展示状态！' }
  ],
};

const information: Ref<IInformationType> = ref(
  ({ ...props.data } as IInformationType) || defaultData
);

watch(props, (newValue) => {
  information.value = ({ ...newValue.data } as IInformationType) || defaultData;
});

const emit = defineEmits(["cancel", "ok"]);

const visible = computed(() => props.show);

const handleOk = () => {
  confirmLoading.value = true;
  from.value
    .validate()
    .then(() => {
      emit("ok", information.value, () => {
        confirmLoading.value = false;
      });
    })
    .catch(() => {
      confirmLoading.value = false;
    });
};

// 上传相关
const imageUrl = ref<string>(information.value.imgUrl || '')
const loading = ref<boolean>(false)
const oldFileList = computed(() => {
  if (information.value.imgUrl) {
    const data: UploadProps['fileList'] = {
      uid: '1',
      name: '图片.png',
      status: 'done',
      url: information.value.imgUrl,
    }
    return [data]
  } else {
    return null
  }
})

const fileList = ref(oldFileList.value || [])

const beforeUpload = (file: UploadFile) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/jpg';
  if (!isJpgOrPng) {
    message.error('只能上传格式为png/jpg/jpeg的文件！');
  }
  const isLt5M = file.size && file.size / 1024 / 1024 < 5;
  if (!isLt5M) {
    message.error('文件大小不能超过5M!');
  }
  return isJpgOrPng && isLt5M;
}

const getBase64 = (img: any, callback: (base64Url: string) => void) => {
  const reader = new FileReader()
  reader.addEventListener('load', () => callback(reader.result as string))
  reader.readAsDataURL(img)
}

const handleChange = (info: UploadChangeParam) => {
  console.log(info)
  if (info.file.status === 'uploading') {
    loading.value = true;
    return;
  }

  if (info.file.status === 'done') {
    getBase64(info.file.originFileObj, (base64Url: string) => {
      imageUrl.value = base64Url;
      loading.value = false;
    })

    information.value.imgUrl = info.file.response.content.url
  }
  if (info.file.status === 'error') {
    loading.value = false;
    message.error('上传出错！');
  }
}

const action = import.meta.env.VITE_UPLOAD_URL



</script>

<template>
  <h-modal v-model:visible="visible" :title="information.id ? '编辑差旅资讯' : '新增差旅资讯'" :width="800" @cancel="$emit('cancel')"
    :confirmLoading="confirmLoading" @ok="handleOk">
    <h-form ref="from" :model="information" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }" :rules="rules">
      <h-form-item label="资讯标题" name="infoTitle">
        <h-input v-model:value="information.infoTitle" />
      </h-form-item>
      <h-form-item label="资讯作者" name="infoAuthor">
        <h-input v-model:value="information.infoAuthor" />
      </h-form-item>
      <h-form-item label="资讯图片" name="imgUrl">
        <h-row>
          <h-col :span="24">
            <h-upload v-model:file-list="fileList" accept="image/png,image/jpeg,image/jpg" :max-count="1"
              class="avatar-uploader" list-type="picture-card" :show-upload-list="false" :action="action"
              @change="handleChange" :before-upload="beforeUpload">
              <img v-if="imageUrl" :src="imageUrl" alt="information" class="imgShow" />
              <div v-else>
                <loading-outlined v-if="loading"></loading-outlined>
                <plus-outlined v-else></plus-outlined>
                <div>上传图片</div>
              </div>
            </h-upload>
          </h-col>
          <h-col :span="24">
            请上传图片的尺寸为<span class="important">72*56</span> 大小不超过<span class="important">2MB</span> 格式为<span
              class="important">png/jpg/jpeg</span>的文件
          </h-col>
        </h-row>
      </h-form-item>
      <h-form-item label="PC端链接" name="jumpLinkPc">
        <h-textarea v-model:value="information.jumpLinkPc" />
      </h-form-item>
      <h-form-item label="移动端链接" name="jumpLinkApp">
        <h-textarea v-model:value="information.jumpLinkApp" />
      </h-form-item>
      <h-form-item label="资讯发布日期" name="infoDate" :label-col="{ span: 4 }" :wrapper-col="{ span: 10 }">
        <h-date-picker v-model:value="information.infoDate" style="width: 100%" />
      </h-form-item>
      <h-form-item label="展示状态" name="showStatus">
        <h-select v-model:value="information.showStatus" allow-clear>
          <h-select-option :value="1">展示</h-select-option>
          <h-select-option :value="0">隐藏</h-select-option>
        </h-select>
      </h-form-item>
    </h-form>
  </h-modal>
</template>


<style lang="less" scoped>
.important {
  color: red;
}


.imgShow {
  width: 100%;
}


.avatar-uploader {
  :deep(.bwdv-upload.bwdv-upload-select-picture-card) {
    width: 144px !important;
    height: 112px !important;
    overflow: hidden;
    border-radius: 8px;
  }
}
</style>
  