<script lang="ts" setup>
import { ConfigProvider, message, Modal } from 'ant-design-vue';
import { computed, onMounted, reactive, ref, watch, watchEffect, toRefs, createVNode } from 'vue';
import { CityResponse, CityItem, ICreatTrip, ICity } from '@haierbusiness-front/common-libs';

import {
  QuestionCircleOutlined,
  EnvironmentFilled,
  PlusOutlined,
  ZoomInOutlined,
  SearchOutlined,
  InfoCircleOutlined,
  DeleteOutlined,
  CheckOutlined,
  CloseOutlined,
  UploadOutlined,
  CheckCircleTwoTone,
  EnvironmentTwoTone,
  EditOutlined,
  TagFilled,
  ExclamationCircleOutlined,
} from '@ant-design/icons-vue';
interface Props {
  creatTripParma?: ICreatTrip;
  isDetail?: boolean;
  isChange?: boolean;
  cityList?: Array<ICity>;
}
const props = defineProps<Props>();

const cityListArr = ref<Array<Array<ICity>>>([]);
const cityList = ref<any>([]);

watch(
  props,
  (newVal, oldVal) => {
    cityList.value = newVal.cityList;
  },
  {
    immediate: true,
    deep: true,
  },
);
watch(
  cityList,
  (newVal, oldVal) => {
    cityListArr.value = [];
    let groups = Math.ceil(newVal.length / 4);

    let cityListTemp = [];

    newVal.forEach((item, index) => {
      // 一组

      if (groups == 1) {
        cityListTemp = [...cityListTemp, item];
      } else {
        cityListTemp = [...cityListTemp, item];
        if ((index + 1) % 4 == 0) {
          cityListArr.value = [...cityListArr.value, cityListTemp];
          cityListTemp = [];
        }
      }

      if (index + 1 == newVal.length) {
        if (groups == 1 && index == 3) {
          cityListArr.value = [...cityListArr.value, cityListTemp, []];
        } else {
          cityListArr.value = [...cityListArr.value, cityListTemp];
        }
      }
    });

    // 详情页摘掉最后一个空对象
    if (props.isDetail && cityListArr.value.length > 1) {
      if (
        cityListArr.value[cityListArr.value.length - 1] &&
        cityListArr.value[cityListArr.value.length - 1].length == 0
      )
        cityListArr.value.pop();
    }
  },
  {
    deep: true,
    immediate: true,
  },
);

// 转换时间格式  2.1
const formateTime = (time: string) => {
  return `${new Date(time).getFullYear()}/${new Date(time).getMonth() + 1}/${new Date(time).getDate()}`;
};
</script>

<template>
  <div style="padding: 30px">
    <div class="travel" style="padding: 0 105px">
      <div class="flex city-list" :class="i % 2 == 0 ? '' : 'reverse'" v-for="(list, i) in cityListArr" :key="i">
        <div class="flex block list-item" v-for="(city, index) in list" :key="index">
          <div class="pict flex">
            <environment-filled class="primary-color font-size-24" />
            <div
              v-if="!(props.isDetail && i + 1 == cityListArr.length && index + 1 == list.length && i % 2 == 0)"
              class="dashed-line"
            ></div>
          </div>

          <div>
            <div class="city mt-10">{{ city.city }}</div>
            <div class="date mt-10" v-if="i == 0 && index == 0">{{ formateTime(city.date) }}</div>
            <div class="date mt-10" style="min-width: 120px" v-else>
              {{ index == 0 ? formateTime(cityListArr[i - 1][3].date) : formateTime(cityListArr[i][index - 1].date) }}
              - {{ formateTime(city.date) }}
            </div>
          </div>
        </div>
        <div
          v-if="i + 1 != cityListArr.length"
          :class="i % 2 == 0 ? 'inflection-point-right' : 'inflection-point-left'"
        ></div>

        <div v-if="!(i % 2 == 0) && !props.isDetail && i + 1 == cityListArr.length" class="dashed-line2"></div>
      </div>
    </div>
    <div class="travel-list">
      <!-- 行程费用 -->
      <div class="travel-item" v-for="(trip, index) in props.creatTripParma.tripList" :key="index">
        <div class="travel-item-head mt-20 flex">
          <div class="head-left flex">
            <div class="head-left-num mr-10">第 {{ index + 1 }} 行程</div>
            <div class="flex font-size-14">
              {{ trip.beginCityName }} - {{ trip.endCityName }}
              <div class="shu ml-10 mr-10"></div>
              {{ formateTime(trip.beginDate) }} - {{ formateTime(trip.endDate) }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
@import url('./trip.less');

.my-tag {
  margin: 0px;
  position: absolute;
  right: 0;
  padding: 0 4px;
  font-size: 10px;
}
:deep(.ant-table-cell) {
  padding-left: 0 !important;
  padding-right: 0 !important;
}
.font-size-16 {
  font-size: 16px;
}
:deep(.ant-select-selection-item) {
  background: rgba(0, 0, 0, 0) !important;
  border: none !important;
}
.travel-item {
  :deep(.ant-select-selection-item) {
    background: rgba(0, 0, 0, 0) !important;
    border: none !important;
    font-size: 14px;
    padding: 0 !important;
  }
  :deep(.ant-input-number-input, .ant-input-borderless, .ant-checkbox-wrapper) {
    font-size: 14px;
  }
  :deep(.ant-checkbox-inner) {
    transform: scale(0.8);
  }
}

.travel {
  flex-wrap: wrap;
}
.reverse {
  flex-direction: row-reverse;
}
.list-item {
  width: 210px;
  min-height: 100px;
  display: flex;
  flex-direction: column;
}
.list-item > .city {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.list-item > .date {
}
.list-item-max {
  flex: 1;
  min-width: 260px;
  max-width: 260px;
  position: relative;
}
.city-list {
  width: 100%;
  position: relative;
  margin-bottom: 16px;
}
.text-hidden {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 100%;
  padding: 0 10px;
}
.inflection-point-right {
  border: 2px dashed #0073e5;
  width: 24px;
  min-height: 118px;
  border-left: none;
  position: absolute;
  top: 12px;
  right: -24px;
  border-radius: 0 8px 8px 0;
}
.inflection-point-left {
  border: 2px dashed #0073e5;
  min-height: 118px;
  border-right: none;
  position: absolute;
  top: 12px;
  width: 60px;
  left: -60px;
  border-radius: 8px 0 0 8px;
}
.dashed-line2 {
  margin-top: 12px;
  margin-left: 3px;
  border-top: 2px dashed #0073e5;
  height: 50%;
  min-width: 40px;
}
.chose-position {
  // position: absolute;
  // left: -8px;
  // top: 14px;
}
:deep(.ant-select-selection-item) {
}
:deep(.city-chose-input) {
  padding: 0 0 0 8px !important;
}
.budget-tooltip {
  :deep(.ant-tooltip-content) {
    width: 400px !important;
  }
  :deep(.ant-tooltip-inner, .ant-spin-container) {
    width: 400px !important;
  }
  :deep(.ant-popover-title) {
    width: 400px !important;
  }
}
.dashed-line {
  margin-left: 3px;
  border-top: 2px dashed #3983e5;
  height: 50%;
  min-width: 100px;
  width: 100%;
}
.pict {
  align-items: center;
  height: 24px;
  align-items: end;
}
.head-left-num {
  width: 70px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #3a83e5;
  color: #fff;
  font-size: 12px;
  border-radius: 5px;
}
</style>