<template>
  <div class="picker">
    <div class="flex">
      <div class="picker-item">
        <el-select
          v-model="panel"
          class="bigscreen"
          placeholder="所有面板"
          filterable
          remote
          clearable
          remote-show-suffix
          no-data-text="暂无数据"
          @change="pageChange"
        >
          <el-option v-for="item in pageList" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div class="picker-item">
        <el-date-picker
          class="bigscreen"
          style="width: 250px"
          v-model="date"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :shortcuts="shortcuts"
          unlink-panels
          @change="dateChange"
        />
      </div>
      <slot name="dateType"></slot>
    </div>

    <div class="flex">
      <div class="picker-item" v-if="isShowArea">
          <el-select
            
            v-model="areaCode"
            class="bigscreen selects"
            collapse-tags
            collapse-tags-tooltip
            placeholder="全部领域"
            filterable
            multiple
            clearable
            :loading="fetching"
            loading-text="加载中"
            remote
            :remote-method="handleAreaSearch"
            no-data-text="暂无数据"
            @change="areaChange"
          >
            <el-option
              v-for="item in areaList"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
      </div>

      <div class="picker-item" v-if="isShowArea">
          <el-select
            v-model="ptCode"
            class="bigscreen selects"
            collapse-tags
            collapse-tags-tooltip
            placeholder="全部平台"
            filterable
            multiple
            clearable
            :loading="fetching"
            loading-text="加载中"
            remote
            :remote-method="handlePtSearch"
            no-data-text="暂无数据"
            @change="ptChange"
          >
            <el-option
              v-for="item in platformList"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
      </div>


      <div class="picker-item" v-if="isShowArea">
          <el-select
            v-model="plCode"
            class="bigscreen selects"
            collapse-tags
            collapse-tags-tooltip
            placeholder="全部产业线"
            filterable
            multiple
            clearable
            :loading="fetching"
            loading-text="加载中"
            remote
            :remote-method="handlePlSearch"
            no-data-text="暂无数据"
            @change="plChange"
          >
            <el-option
              v-for="item in industryList"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
      </div>

      <div class="picker-item">
        <template
          v-if="
            checkUserGroups(
              [
                UserGroupSystemConstant.SUPER_MANAGE.groupId,
                UserGroupSystemConstant.REPORT_CONTROL.groupId,
              ],
              'OR'
            )
          "
        >
          <el-select
            v-model="budgetDepartment"
            class="bigscreen selects"
            collapse-tags
            collapse-tags-tooltip
            placeholder="全部预算部门"
            filterable
            multiple
            clearable
            :loading="fetching"
            loading-text="加载中"
            remote
            :remote-method="handleBudgetSearch"
            no-data-text="暂无数据"
            @change="budgetChange"
          >
            <el-option
              v-for="item in settleDepartment"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </template>
        <template v-else>
          <el-select
            v-model="budgetDepartment"
            class="bigscreen selects"
            collapse-tags
            collapse-tags-tooltip
            placeholder="全部预算部门"
            filterable
            multiple
            clearable
            :loading="fetching"
            loading-text="加载中"
            remote
            :remote-method="handleBudgetSearch"
            no-data-text="暂无数据"
            @change="budgetChange"
          >
            <el-option
              v-for="item in settleDepartment"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </template>
      </div>
      <div class="picker-item">
        <template
          v-if="
            checkUserGroups(
              [
                UserGroupSystemConstant.SUPER_MANAGE.groupId,
                UserGroupSystemConstant.REPORT_CONTROL.groupId,
              ],
              'OR'
            )
          "
        >
          <el-select
            v-model="company"
            class="bigscreen selects"
            collapse-tags
            collapse-tags-tooltip
            placeholder="全部结算单位"
            filterable
            multiple
            clearable
            :loading="fetching"
            loading-text="加载中"
            remote
            :remote-method="handleSearch"
            no-data-text="暂无数据"
            @change="companyChange"
          >
            <el-option
              v-for="item in settleCompany"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </template>
        <template v-else>
          <el-select
            v-model="company"
            class="bigscreen selects"
            collapse-tags
            collapse-tags-tooltip
            placeholder="全部结算单位"
            filterable
            multiple
            clearable
            :loading="fetching"
            loading-text="加载中"
            remote
            :remote-method="handleSearch"
            no-data-text="暂无数据"
            @change="companyChange"
          >
            <el-option
              v-for="item in settleCompany"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </template>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { nextTick, onMounted, computed, ref } from "vue";
import { debounce } from "lodash";
import moment from "moment";
import { useBoardStore } from "@haierbusiness-front/utils/src/store/board";
// import { querySettleCompany } from '@haierbusiness-front/apis/src/data/board';
import { EventBus } from "../eventBus";
// import { value } from "dom7";
import { useRouter, useRoute } from "vue-router";
import { UserGroupSystemConstant } from "@haierbusiness-front/common-libs";
import { checkUserGroups } from "@haierbusiness-front/utils/src/authorityUtil";
import { reportApi } from "@haierbusiness-front/apis";

import { findTreesByUrls } from "@haierbusiness-front/utils";
import { storeToRefs } from "pinia";
import globalPinia from "@haierbusiness-front/utils/src/store/store";
import { applicationStore } from "@haierbusiness-front/utils/src/store/applicaiton";
const { resource } = storeToRefs(applicationStore(globalPinia));
import { errorModal, getCurrentRouter, routerParam } from "@haierbusiness-front/utils";
const router = getCurrentRouter();

const yesterday = moment().subtract(1, "days");
const pattern = "YYYY-MM-DD";
const store = useBoardStore();
const fetching = ref(false);
const date = ref<[string, string]>(["2023-01-01", moment(yesterday).format(pattern)]);

store.setDate(date.value as any);
const panel = ref("");
const company = ref();
const companyList: any = ref([]);
const pageList = ref([] as Array<{ label: string; value: string }>);

function combine_ids(ids: any) {
  if (ids.length == 1) return "'" + ids[0] + "'";
  return ids.length ? "'" + ids.join("','") + "'" : "";
}

const pageChange = (value: any) => {
  let url = window.origin + window.location.pathname + "#" + value;
  window.open(url);
};
const currentPower: any = findTreesByUrls(resource.value, ["/board"]);

onMounted(() => {
  pageList.value = currentPower[0].children.map((item: { name: string; url: string }) => {
    return {
      label: item.name,
      value: item.url,
    };
  });
  // if (getUserGroup()?.includes('157')) store.setData('BCC')
  // else if (getUserGroup()?.includes('158')) store.setData('GEMS')
  // else if (getUserGroup()?.includes('159')) store.setData('KEMS')
  // else if (getUserGroup()?.includes('160')) store.setData('RRSGEMS')
  // else if (getUserGroup()?.includes('161')) store.setData('MICROd')
});

//日期范围发生变化
const dateChange = (value: any) => {
  if (!value) {
    store.setDate([]);
    EventBus.emit("refresh");
    return;
  }
  store.setDate([moment(value[0]).format(pattern), moment(value[1]).format(pattern)]);
  EventBus.emit("refresh");
};
//快捷选择面板
const shortcuts = [
  {
    text: "昨天",
    value() {
      return [moment(yesterday).format(pattern), moment().format(pattern)];
    },
  },
  {
    text: "最近一周",
    value() {
      return [
        moment(yesterday).subtract(1, "weeks").format(pattern),
        moment(yesterday).format(pattern),
      ];
    },
  },
  {
    text: "最近两周",
    value() {
      return [
        moment(yesterday).subtract(2, "weeks").format(pattern),
        moment(yesterday).format(pattern),
      ];
    },
  },
  {
    text: "最近一月",
    value() {
      return [
        moment(yesterday).subtract(1, "months").format(pattern),
        moment(yesterday).format(pattern),
      ];
    },
  },
  {
    text: "最近三月",
    value() {
      return [moment().subtract(3, "months").format(pattern), moment().format(pattern)];
    },
  },
  {
    text: "最近一年",
    value() {
      return [
        moment(yesterday).subtract(1, "years").format(pattern),
        moment(yesterday).format(pattern),
      ];
    },
  },
];
//结算公司选中
const companyChange = (value: string) => {
  store.setCompany(combine_ids(value));
  EventBus.emit("refresh");
};
EventBus.on((event, params) => {
  if (event == "refresh" && params && params.from == "date") {
    if (params.name.split("-").length == 1) {
      date.value = [
        moment(params.name).startOf("year").format("YYYY-MM-DD"),
        moment(params.name).endOf("year").format("YYYY-MM-DD"),
      ];
      store.setDate([
        moment(params.name).startOf("month").format("YYYY-MM-DD"),
        moment(params.name).endOf("month").format("YYYY-MM-DD"),
      ]);
    } else if (params.name.split("-").length == 2) {
      date.value = [
        moment(params.name).startOf("month").format("YYYY-MM-DD"),
        moment(params.name).endOf("month").format("YYYY-MM-DD"),
      ];
      store.setDate([
        moment(params.name).startOf("month").format("YYYY-MM-DD"),
        moment(params.name).endOf("month").format("YYYY-MM-DD"),
      ]);
    } else {
      date.value = [params.name, params.name];
      store.setDate([params.name, params.name]);
    }
  }
});

const settleCompany = ref<any>([]);
const querySettleCompany = async (val: string) => {
  //查询结算单位
  getPowerByApprove(val,2);


  // const res = await reportApi.querySettleCompany({
  //   type: getTypeKey(router.currentRoute.value.path),
  //   moduleType: 2,
  //   keyword:keyword,
  // });
  // settleCompany.value = res;
};
const handleSearch = (val: string) => {
  //管理员搜索
  querySettleCompany(val);
};

const areaCode = ref();
const ptCode = ref()
const plCode = ref()

// const areaList= ref<any>([]);
// const queryArea = async (keyword: string) => {
//   //查询领域
//   const res = await reportApi.queryAreaList({
//     type: getTypeKey(router.currentRoute.value.path),
//     moduleType: 2,
//     keyword:keyword,
//   });
//   areaList.value = res;
// };
// const handleAreaSearch = (val: string) => {
//   queryArea(val);
// };

// 根据当前路由判断是否展示 超市系统不展示 平台、产业线、领域下拉框
const isShowArea = computed(() => {
  const hideList = [
    "/data/board/supermarket/maker",
    "/data/board/supermarket/business"
  ]
  return hideList.indexOf(router.currentRoute.value.path) < 0
});

// 查询领域、平台、产业线
const areaList= ref([]);
const platformList= ref([]);
const industryList= ref([]);

// 根据类型查询不同权限类型 permissionType 1:预算部门 2: 结算单位 3:领域 4:平台 5:产业线
const getPowerByApprove = async (name: string, permissionType: number) => {
  const data = await reportApi.getPowerByApprove(name,permissionType, 2, getTypeKey(router.currentRoute.value.path));
  switch (permissionType) {
    case 1:
     settleDepartment.value = data;
      break;

    case 2:
      settleCompany.value = data;
      break;
    case 3:
      areaList.value = data;
      break;
    case 4:
      platformList.value = data;
      break;
    case 5:
      industryList.value = data;
      break;
    default:
      break;
  }
};

const handleAreaSearch = (val: string) => {
  getPowerByApprove(val,3);
};

const handlePtSearch = (val: string) => {
  getPowerByApprove(val,4);
};

const handlePlSearch = (val: string) => {
  getPowerByApprove(val,5);
};


const budgetDepartment = ref();

//预算部门选中
const budgetChange = (value: string) => {
  store.setBudgetDepartment(combine_ids(value));
  EventBus.emit("refresh");
};

// 领域选中
const areaChange = (value: string) => {
  store.setAreaCode(combine_ids(value));
  EventBus.emit("refresh");
};

const ptChange =  (value: string) => {
  store.setPtCode(combine_ids(value));
  EventBus.emit("refresh");
};

const plChange =  (value: string) => {
  store.setPlCode(combine_ids(value));
  EventBus.emit("refresh");
};

const handleBudgetSearch = (val: string) => {
  querySettleDepartment(val);
};

const settleDepartment: any = ref([]);
const querySettleDepartment = async (val: string) => {
  //查询部门
  getPowerByApprove(val,1);
  // const data = await reportApi.querySettleDepartment({
  //   type: getTypeKey(router.currentRoute.value.path),
  //   moduleType: 2,
  //   keyword:keyword,
  // });
  // if (data && data.length > 0) {
  //   settleDepartment.value = data;
  // }
};
// 获取name
const getTypeKey = (url: number | string) => {
  const resultMap: any = {
    "/data/board/booking-hotel": "localHotel",
    "/data/board/ordering-food": "localRestaurant",
    "/data/board/travel/internal": "domesticAirTickets",
    "/data/board/travel/external": "internationalAirfare",
    "/data/board/travel/hotel": "hotel",
    "/data/board/travel/taxi": "taxi",
    "/data/board/travel/train": "trainTicket",
    "/data/board/mice/offsite": "miceOffsite",
    "/data/board/mice/local": "miceLocal",
    "/data/board/travel/index": "travelOverview",
    default: "",
  };
  return resultMap[url] || resultMap.default;
};
onMounted(async () => {
  // querySettleCompany({
  //   type: getTypeKey(router.currentRoute.value.path),
  //   moduleType: 2,
  //   keyword: "",
  // }); //结算单位
  // querySettleDepartment({
  //   type: getTypeKey(router.currentRoute.value.path),
  //   moduleType: 2,
  //   keyword: "",
  // }); //预算部门
  // queryArea({
  //   type: getTypeKey(router.currentRoute.value.path),
  //   moduleType: 2,
  //   keyword: "",
  // })
  getPowerByApprove("", 1);
  getPowerByApprove("", 2);
  getPowerByApprove("", 3);
  getPowerByApprove("", 4);
  getPowerByApprove("", 5);
});
</script>
<style scoped lang="less">
.picker {
  display: flex;
  justify-content: space-between;
  padding-right: 50px;

  &-item {
    margin: 0 10px;
  }
}

.flex {
  display: flex;
}
</style>
<style lang="less">
.bigscreen {
  --el-border-color: #0e2b63;
}

// .el-input__wrapper {
//     background-color: rgba(0, 0, 0, 0) !important
// }

// .el-popper.is-light {
//     background-color: rgba(0, 0, 0, 0.85) !important;
// }

// .bigscreen.bwdv-picker,.bigscreen.bwdv-select{
//     background: #0E2B63;
//     border-color:#0E2B63;
//     border-radius: 999px;
//     width: 250px;
//     padding-left: 20px;
//     color:#fff;
//     .bwdv-picker-suffix{
//         color:#fff;
//     }
//     .bwdv-picker-input > input{
//         color: #fff;
//     }
//     .bwdv-picker-clear{
//         background: #0E2B63;
//     }
// }
// .bigscreen.bwdv-select{
//     padding-left: 10px;
//     .bwdv-select-selector{
//         background: none;
//         border-color: transparent;
//         outline: none;
//     }
//     .bwdv-select-clear{
//         background: #0E2B63;
//     }
//     .bwdv-select-arrow{
//         color:#fff
//     }
// }
</style>

<style lang="less">
.el-select__collapse-tags {
  flex-direction: column;
  align-items: start;
}
.selects {
  .el-select__tags {
    // 调整输入框里面的多选不换行
    flex-wrap: nowrap !important;
  }

  .el-tag:first-child {
    width: 75px !important;
  }

  .el-select__tags-text {
    max-width: 55px !important;
  }
}
</style>
