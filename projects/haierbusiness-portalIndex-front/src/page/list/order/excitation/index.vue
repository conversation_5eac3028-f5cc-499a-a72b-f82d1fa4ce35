<template>
  <div class="container">
    <h-form
      ref="from"
      :model="searchKey"
      @finish="onReFilterChange"
      style="width: 100%"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
    >
      <h-row :gutter="24" class="search">
        <h-col :span="8">
          <h-form-item has-feedback label="订单编号" name="orderCode">
            <h-input v-model:value="searchKey.orderCode" placeholder="订单编号" allow-clear />
          </h-form-item>
        </h-col>

        <h-col :span="8">
          <h-form-item has-feedback label="操作人" name="applyCodeOrName">
            <h-input v-model:value="searchKey.applyCodeOrName" placeholder="操作人" allow-clear />
          </h-form-item>
        </h-col>

        <h-col :span="8">
          <h-form-item has-feedback label="下发状态" name="rechargeStatus">
            <h-select v-model:value="searchKey.rechargeStatus" multiple placeholder="订单状态">
              <h-select-option value="">全部</h-select-option>
              <h-select-option v-for="(item, index) in approvalState" :key="index" :value="item.value">{{
                item.label
              }}</h-select-option>
            </h-select>
          </h-form-item>
        </h-col>

        <h-col :span="16">
          <h-form-item
            has-feedback
            label="操作时间"
            name="timeRange"
            :label-col="{ span: 4 }"
            :wrapper-col="{ span: 20 }"
          >
            <h-range-picker
              v-model:value="searchKey.timeRange"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="onTimeChange"
              style="width: 100%"
            />
          </h-form-item>
        </h-col>
      </h-row>
      <h-row justify="center">
        <div class="flexCon">
          <h-space>
            <h-button @click="handleReset">重置</h-button>
            <h-button type="primary" html-type="submit">查询</h-button>
            <h-button @click="handleExport">导出</h-button>
          </h-space>
        </div>
      </h-row>
    </h-form>

    <h-spin :spinning="dataLoading">
      <template v-if="dataList && dataList.length > 0">
        <div class="banner-contain list" v-for="(data, index) in dataList" :key="index">
          <div class="banner-contain-bottom">
            <div class="banner-contain-data">
              <div>
                <div class="my-title">订单编号:{{ data.orderCode }}</div>
              </div>
              <div class="data-btn">
                <!-- 订单状态 -->
                <a-tooltip>
                  <template #title>订单状态</template>
                  <h-tag :color="AccountRechargeStatusTagColorMap[data.rechargeStatus]">
                    <span>{{ AccountRechargeStatusEnum[data.rechargeStatus] || '' }}</span>
                  </h-tag>
                </a-tooltip>
              </div>
            </div>
            <!-- 个人信息 -->

            <a-descriptions class="banner-detail" :column="3">
              <a-descriptions-item label="总计金额">{{ data.amountSum }}</a-descriptions-item>
              <a-descriptions-item label="实际下发金额">{{ data.realAmount }}</a-descriptions-item>
              <a-descriptions-item label="下发进度">{{ data.failCount + data.successCount }}/{{ data.totalNum }} {{ data.failCount ? (data.failCount + '个失败') : '' }}</a-descriptions-item>
              <a-descriptions-item label="操作人">{{ data.applyName || '-' }}</a-descriptions-item>
              <a-descriptions-item label="操作时间">{{ formatDateTime(data.gmtCreate) }}</a-descriptions-item>
            </a-descriptions>

            <div class="btn-contain">
              <a-button
                class="mr-10"
                type="primary"
                size="small"
                :icon="h(ContainerOutlined)"
                @click="goToDetail(data.id)"
                >详情</a-button
              >

              <!-- <a-button
                class="mr-10"
                type="primary"
                size="small"
                v-if="data.processStatus != '0'"
                :icon="h(CheckCircleOutlined)"
                @click="goToApproval(data.processCode)"
                >审批详情</a-button
              > -->

              <a-button
                class="mr-10"
                size="small"
                v-if="data.rechargeStatus == '30'"
                :icon="h(EditOutlined)"
                @click="rechargeAgain()"
                >再来一单</a-button
              >
            </div>
          </div>
        </div>
      </template>
      <div class="page" v-show="pagination.total && pagination.total > 0">
        <h-pagination
          v-model:current="pagination.current"
          show-size-changer
          show-quick-jumper
          :total="pagination.total"
          @change="onPageChange"
        />
      </div>

      <div class="empty" v-if="!dataList || dataList.length === 0">
        <h-empty />
      </div>
    </h-spin>
  </div>
</template>
<script setup lang="ts">
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue';
import {
  Button as hButton,
  Col as hCol,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Tag as hTag,
  Spin as hSpin,
  Empty as hEmpty,
  Space as hSpace,
  Pagination as hPagination,
  Checkbox as hCheckbox,
  message,
  FormItemRest as hFormItemRest,
} from 'ant-design-vue';
import {
  RechargeStatusEnum,
  TripDocumentStatus,
  TeamListStatusEnum,
  teamListStateTagColorMap,
  TripChangeApprovalStatus,
  TripChangeApprovalStatusToTagColor,
  TripChangeStatus,
  TripBudgeStatus,
  RRechargeQueryParams,
  TCteateTeam,
  IUserInfo,
  ITraveler,
  TripApprovalStatusToTagColor,
  TripDocumentStatusToTagColor,
  TripChangeStatusToTagColor,
  TripBudgeStatusToTagColor,
  IDataListItem,
  ICity,
  CityItem,
  IUserListRequest,
  excitationStatusEnum,
} from '@haierbusiness-front/common-libs';
import cityChose from '@haierbusiness-front/components/cityChose/index.vue';

import { useOrderSearch } from '@haierbusiness-front/composables';
import { computed, onMounted, reactive, ref, watch, h } from 'vue';
import type { LocalrestType, LocalrestFilter } from '@haierbusiness-front/common-libs';
import { localrestApi, rechargeApi } from '@haierbusiness-front/apis';
import {
  PaymentTypeEnum,
  RestaurantOrderStateEnum,
  orderStateTagColorMap,
  LocalHotelPaymentTypeEnum,
  RestaurantOrderApprovalStateEnum,
  approvalStateTagColorMap,
  RestaurantOrderPayMentStateEnum,
  paymentStateTagColorMap,
} from '@haierbusiness-front/common-libs';
import { getEnumOptions } from '@haierbusiness-front/utils';
// import { useOrderStore } from "@/store"
import { useRoute, useRouter } from 'vue-router';
import { storeToRefs } from 'pinia';
import globalPinia from '@haierbusiness-front/utils/src/store/store';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { teamApi } from '@haierbusiness-front/apis';
import { DataType, usePagination, useRequest } from 'vue-request';
import type { PaginationProps } from 'ant-design-vue';
import dayjs from 'dayjs';
import { Modal } from 'ant-design-vue';
import { createVNode } from 'vue';
import { AccountRechargeStatusEnum,AccountRechargeStatusTagColorMap } from '@haierbusiness-front/common-libs';
import {
  QuestionCircleOutlined,
  EnvironmentFilled,
  PlusOutlined,
  ZoomInOutlined,
  DeleteOutlined,
  CheckOutlined,
  CloseOutlined,
  UploadOutlined,
  CheckCircleTwoTone,
  EnvironmentTwoTone,
  EditOutlined,
  CheckCircleOutlined,
  
  UndoOutlined,
  HighlightOutlined,
  ExclamationCircleOutlined,
  ShareAltOutlined,
  DollarOutlined,
  TagFilled,
  SearchOutlined,
  MoreOutlined,
  DownOutlined,
  ContainerOutlined,
  StopOutlined,
} from '@ant-design/icons-vue';
import { userInfo } from 'os';

const { loginUser } = storeToRefs(applicationStore(globalPinia));

const route = useRoute();

const labelCol = {
  span: 8,
};
const wrapperCol = {
  span: 16,
};
const router = useRouter();

// 用户选择
const params = ref<IUserListRequest>({
  pageNum: 1,
  pageSize: 20,
});
const handleExport = () => {
  rechargeApi.exportExcitationList(searchKey).then((res) => {
    console.log('🚀 ~ rechargeApi.exportExcitationList ~ res:', res);
    // window.open(res);
  });
};

const userNameChange = (userInfo: IUserInfo) => {
  searchKey.applyName = userInfo?.nickName || '';
  // searchKey.transactorCode = userInfo?.username || ''
};

// 2024-01-02 转换为 2024年01月02日
const formatDate = (time: string | undefined) => {
  if (!time) {
    return '';
  }
  return dayjs(time).format('YYYY年MM月DD日');
};

// 2024-01-02 转换为 2024年01月02日
const formatDateTime = (time: string | undefined) => {
  if (!time) {
    return '';
  }
  return dayjs(time).format('YYYY年MM月DD日 HH:mm:ss');
};
const rechargeAgain = () => {
  router.push('/excitation/addExcitation');
};

const processUrl = import.meta.env.VITE_BUSINESS_PROCESS_URL

// 审批流页面
const goToApproval = (code) => {
  const url = processUrl + `?code=${code}#/details`
  window.open(url);
}
// 审批状态
const approvalState = computed(() => {
  return getEnumOptions(excitationStatusEnum, true);
});

const recallTeam = (id: string | undefined) => {
  const param: TCteateTeam = {
    id,
  };

  Modal.confirm({
    title: '您确认吗?',
    icon: createVNode(ExclamationCircleOutlined),
    content: '确认要撤销这条团队票申请吗?',
    okText: '确认',
    cancelText: '取消',
    onOk() {
      teamApi.recallTeam(param).then((res) => {
        getTeamList();
      });
    },
    onCancel() {
      console.log('Cancel');
    },
  });
};

const from = ref();
// 本地跳转用于调试 跳转变更单 跳转详情
const teamDetail = import.meta.env.VITE_BUSINESS_TEAM_URL;

const goToDetail = (id: string) => {
  router.push('/excitation/excitationDetail?id=' + id);
};

const businesstravel = import.meta.env.VITE_BUSINESSTRAVEL_URL;

const searchKey = reactive<RRechargeQueryParams>({
  orderCode: '', //订单单号
  serialCode: '',
  statusList: '',
  endTime: '',
  startTime: '',
  applyName: '',
  payCode: '',

  pageNum: 1,
  pageSize: 10,
});
const pagination = reactive<PaginationProps>({
  current: 1,
  total: 0,
  pageSize: 10,
});

const onReFilterChange = () => {
  searchKey.pageNum = 1;
  searchKey.pageSize = 10;
  pagination.pageSize = 10;
  pagination.current = 1;
  searchKey.orderCode = searchKey.orderCode?.trim()
  searchKey.applyCodeOrName = searchKey.applyCodeOrName?.trim()

  getTeamList();
};

const getTeamList = async () => {
  dataLoading.value = true;

  const res = await rechargeApi.getBoostList(searchKey);
  pagination.total = res?.total || 0;
  dataList.value = res?.records || [];
  dataLoading.value = false;
};

onMounted(() => {
  getTeamList();
});

const dataList = ref<Array<TCteateTeam>>([]);

const dataLoading = ref<boolean>(false);

const onPageChange = (page: number, pageSize: number) => {
  pagination.pageSize = pageSize;
  pagination.current = page;
  searchKey.pageNum = page;
  searchKey.pageSize = pageSize;
  getTeamList();
};

const handleReset = () => {
  from.value && from.value.resetFields();
  if (!searchKey.timeRange || searchKey.timeRange.length != 2) {
    searchKey.startTime = '';
    searchKey.endTime = '';
  }
  onReFilterChange();
};

const onTimeChange = (dateRange: string[]) => {
  if (dateRange && dateRange.length === 2) {
    searchKey.startTime = dateRange[0]+ " 00:00:00";
    searchKey.endTime = dateRange[1]+ " 23:59:59";
  } else {
    searchKey.startTime = '';
    searchKey.endTime = '';
  }
};
</script>

<style scoped lang="less">
.empty {
  margin-top: 50px;
  border: 1px solid #f0f0f0;
  padding: 42px 24px 50px;
}
.container {
  display: flex;
  width: 100%;
  flex-direction: column;

  .search {
    display: flex;
    width: 100%;
  }
}

.list {
  display: flex;
  margin-top: 20px;
  width: 100%;
  flex-direction: column;

  .order-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    margin-bottom: 20px;

    .order-header {
      width: 100%;
      background-color: #f5f5f5;
      height: 43px;
      line-height: 43px;
      padding-left: 20px;
      color: #333;
      font-size: 12px;
      border: 1px solid #eaeaea;
      display: flex;
      justify-content: space-between;

      .order-header-left {
        display: flex;
      }
      .order-header-right {
        display: flex;
        align-items: center;
      }
    }
    .order-body {
      display: flex;
      width: 100%;
      border: 1px solid #eaeaea;
      border-top: 0;
      flex-direction: row;

      .first {
        display: flex;
        flex: 3;
        padding: 24px 10px;
      }
      .second {
        display: flex;
        flex: 2;
        border-left: 1px solid #eaeaea;
        padding: 24px 10px;
      }
      .three {
        display: flex;
        flex: 2;
        border-left: 1px solid #eaeaea;
        padding: 24px 10px;
      }
      .last {
        display: flex;
        flex: 3;
        border-left: 1px solid #eaeaea;
        padding: 24px 10px;
      }

      .second,
      .three {
        .title {
          width: 80px;
          text-align: right;
          line-height: 20px;
        }

        .value {
          padding-left: 5px;
          width: calc(100% - 80px);
          line-height: 20px;
        }
      }

      .first,
      .last {
        .title {
          width: 100px;
          text-align: right;
          line-height: 20px;
        }

        .value {
          padding-left: 5px;
          width: calc(100% - 100px);
          line-height: 20px;
        }
      }
    }
    .order-footer {
      width: 100%;
      background-color: #f5f5f5;
      height: 43px;
      line-height: 43px;
      padding-right: 8px;
      color: #333;
      font-size: 12px;
      border-left: 1px solid #eaeaea;
      border-right: 1px solid #eaeaea;
      border-bottom: 1px solid #eaeaea;
      display: flex;
      flex-direction: row-reverse;
      align-items: center;
    }
  }
}

.page {
  display: flex;
  width: 100%;
  flex-direction: row-reverse;
  margin-bottom: 20px;
}

.banner-contain {
  width: 100%;
  padding-top: 16px;
  margin-bottom: 20px;
  box-sizing: border-box;
  // background-image: url('@/assets/image/banner/baner-contain-bac.png');
  background-size: 100% 100%;
  .ban-con-title {
    display: flex;
    justify-content: space-between;
    padding-right: 24px;
    position: relative;
    .ban-title-left {
      font-weight: 600;
      font-size: 18px;
      color: #3983e5;
      padding-left: 24px;
      .ban-border {
        position: absolute;
        top: 0;
        left: 2px;
        width: 4px;
        height: 21px;
        background: linear-gradient(180deg, #3983e5 0%, #6bb9f4 100%);
      }
    }
    .ban-title-right {
      /* font-weight: 500; */
      font-size: 14px;
      color: #3983e5;
      display: flex;
      align-items: center;
      img {
        width: 18px;
        height: 18px;
      }
    }
  }
  .banner-contain-bottom {
    width: 100%;
    background-color: #fff;
    border-radius: 8px;
    .banner-contain-data {
      background-color: #f5f5f5;
      display: flex;
      height: 40px;
      align-items: center;
      padding: 0 12px;
      color: #262626;
      justify-content: space-between;
      font-weight: 400;
      border-bottom: 1px solid #eaeaea;
      width: 100%;
      background-color: #f5f5f5;
      height: 43px;
      line-height: 43px;
      padding-left: 20px;
      color: #333;
      font-size: 12px;
      border: 1px solid #eaeaea;
      div {
        display: flex;
        align-items: center;
      }
      .my-title {
        font-size: 14px;
        font-weight: 600;
        margin-right: 10px;
      }
      .data-one {
        width: 195px;
        height: 28px;
        border-radius: 2px;
        box-sizing: border-box;
        padding-left: 8px;
        :deep(.ant-select-selector) {
          width: 195px;
          height: 28px !important;
          line-height: 28px;
          background: #fafafa;
          border: 0;
        }
        :deep(.ant-select-selection-item) {
          line-height: 28px;
          font-weight: 500;
          box-shadow: 0 0 0 0 rgba(0, 0, 0, 0) !important;
        }
      }
      .data-time {
        font-size: 13px;
        margin-left: 20px;
        img {
          width: 16px;
          height: 16px;
          margin-right: 2px;
        }
        .peice-mar {
          margin-right: 8px;
        }
      }
      .data-btn {
        display: flex;
        margin-left: 33px;
        .data-sta {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 78px;
          height: 22px;
          font-size: 12px;
          color: #10a710;
          background: #f6ffed;
          border-radius: 2px;
          border: 1px solid #b7eb8f;
          img {
            width: 12px;
            height: 12px;
          }
        }
        .data-tra {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 52px;
          height: 22px;
          background: #f0f9ff;
          border-radius: 2px;
          border: 1px solid #a1d1ff;
          font-size: 12px;
          color: #0073e5;
          margin-left: 4px;
        }
      }
    }
    .banner-detail {
      padding: 20px 40px;
      background: #ffffff;
      box-sizing: border-box;
      font-weight: 400;
      display: flex;
      padding-right: 16px;
      font-family: '';
      display: flex;
      width: 100%;
      border: 1px solid #eaeaea;
      border-top: 0;
      flex-direction: row;
      .detail-info-i {
        display: flex;
        font-size: 14px;
        color: #8c8c8c;
        height: 20px;
        align-items: center;
        margin-bottom: 5px;

        .info-i-l {
          display: flex;
          align-items: center;
          width: 84px;
        }
        img {
          width: 16px;
          height: 16px;
        }
        .names {
          color: #262626;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .info-main {
          width: 200px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          font-family: '';
          /* margin-left: 8px; */
        }
      }
    }
    .banner-step {
      overflow: auto;
      display: flex;
      width: 800px;
      height: 72px;
      background: #fafafa;
      border-radius: 4px;
      display: flex;
      box-sizing: border-box;
      padding: 0 30px;
      color: rgba(0, 0, 0, 0.85);
      .step-item {
        display: flex;
        min-width: 200px;
        padding-top: 15px;
      }
      .step-item-start {
        min-width: 60px;
      }
      .step-name {
        // min-width: 145px;
        width: 45px;
        position: relative;
        > span {
          font-size: 14px;
        }
        .step-c {
          position: absolute;
          color: #8c8c8c;
          bottom: 32px;
          left: 0;
          width: 100px;
          height: 30px;
          display: flex;
          align-items: center;
          justify-content: center;
          .city-name {
            flex: 1;
            overflow: hidden;
            display: inline-block;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
        .step-t {
          position: absolute;
          color: #8c8c8c;
          bottom: 14px;
          left: 0;
          width: 140px;
        }
      }
      .idol {
        border-bottom: 1px dashed #d9d9d9;
        display: flex;
        align-items: center;
        justify-content: center;
        flex: 1;
        height: 11px;
        max-width: 100%;
        margin: 0 23px;
        min-width: 100px;
        .idol-img {
          position: relative;
          top: 3px;
          /* position: absolute;
                  top: -2px;
                  left: 50%;
                  transform: translateX(-50%); */
        }
        img {
          width: 24px;
          height: 24px;
          margin-left: 4px;
        }
      }
    }
    .hotal-img {
      width: 24px;
      height: 24px;
      margin-left: 4px;
    }
  }

  .btn-contain {
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-right: 16px;
    width: 100%;
    background-color: #f5f5f5;
    height: 43px;
    // line-height: 43px;
    padding-right: 8px;
    color: #333;
    font-size: 12px;
    border-left: 1px solid #eaeaea;
    border-right: 1px solid #eaeaea;
    border-bottom: 1px solid #eaeaea;
    display: flex;
    align-items: center;
    .btn {
      cursor: pointer;
      // width: 76px;
      // height: 24px;
      // background: linear-gradient(180deg, #6bb9f4 0%, #3983e5 100%);
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 14px;
      color: #ffffff;
      font-weight: 400;
      border-radius: 3px;
      img {
        width: 14px;
        height: 14px;
        margin-right: 4px;
      }
    }
    .btn-yu {
      position: relative;
      .card-list {
        position: absolute;
        top: 28px;
        right: -10px;
        z-index: 999;
        width: 340px;
        /* height: 202px; */
        background: #ffffff;
        box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08),
          0px 3px 6px -4px rgba(0, 0, 0, 0.12);
        border-radius: 8px;
        padding: 20px;
        box-sizing: border-box;

        .triangle-con {
          position: absolute;
          width: 70px;
          height: 15px;
          top: -15px;
          right: 10px;
        }

        .triangle {
          float: right;
          margin-right: 10px;
          margin-top: 5px;
        }
        .card-list-item {
          display: flex;
          // justify-content: space-between;
          flex-direction: row;
          border-bottom: 1px solid #eee;
          padding-bottom: 17px;
          margin-top: 12px;
          &:first-child {
            margin-top: 0px;
          }
          &:last-child {
            border-bottom: 0px;
            padding-bottom: 0px;
          }
          /* align-items: center; */
          .item-l {
            width: 34px;
            height: 34px;
            margin-right: 0;
          }
          .banner-step {
            margin-left: 12px;
            width: 210px;
            height: 30px;
            overflow: inherit;
            display: flex;
            box-sizing: border-box;
            flex-direction: row;
            justify-content: start;
            padding: 0;
            background: #fff;
            .step-name_ {
              width: 75px;
              position: relative;
              font-family: '';
              span {
                overflow: hidden;
                white-space: nowrap;
                display: inline-block;
                width: 100%;
                text-overflow: ellipsis;
              }
              .step-t {
                bottom: 0px;
                position: absolute;
                font-size: 12px;
                color: #8c8c8c;
                left: 0;
                width: 55px;
                font-weight: 400;
              }
            }
            .idol_ {
              width: 40px;
              border-bottom: 1px dashed #d9d9d9;
              position: relative;
              margin: 0 7px;
              top: -6px;
            }
          }
          .btn-step_ {
            width: 44px;
            height: 24px;
            background: rgba(107, 185, 244, 0.1);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: rgba(57, 131, 229, 0.8);
            font-size: 14px;
          }
          .btn-reserve {
            background: linear-gradient(180deg, #6bb9f4 0%, #3983e5 100%);
            border-radius: 12px;
            color: #fff;
          }
        }
      }
      .card-more-ban {
        width: 82px;
        // height: 100px;
        background: #ffffff;
        box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08),
          0px 3px 6px -4px rgba(0, 0, 0, 0.12);
        border-radius: 4px;
        position: absolute;
        top: 30px;
        right: -10px;
        padding: 6px 17px 0;
        box-sizing: border-box;
        z-index: 99;

        .triangle-con {
          position: absolute;
          width: 70px;
          height: 15px;
          top: -15px;
          right: 9px;
        }

        .triangle {
          float: right;
          margin-right: 10px;
          margin-top: 5px;
        }

        // .triangle{
        //   position: absolute;
        //   width:20px;
        //   height: 20px;
        //   top: -15px;
        //   right: 20px;
        // }
        .more-ban-items:hover {
          color: rgba(255, 77, 79, 1);
        }
        .more-ban-items {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 14px;
          height: 30px;
          line-height: 30px;
          color: rgba(0, 0, 0, 0.85);
          img {
            width: 14px;
            height: 14px;
          }
        }
      }
    }

    .btn-et:hover {
      border: 1px solid #2793f2;
    }

    .btn-et {
      border: 1px solid #3983e5;
      background: #fff;
      margin-left: 6px;
      color: #3983e5;
    }
    .btnMouseInter {
      color: #40a9ff;
      border: 1px solid #40a9ff;
    }
  }

  .add-apply:hover {
    background: linear-gradient(180deg, #3983e5 0%, #6bb9f4 100%);
  }

  .add-apply {
    width: 202px;
    height: 48px;
    margin-top: 8px;
    margin: 8px auto 0;
    background: linear-gradient(180deg, #6bb9f4 0%, #3983e5 100%);
    box-shadow: 0px 2px 8px 0px rgba(60, 134, 230, 0.26);
    border-radius: 24px;
    font-weight: 500;
    font-size: 18px;
    color: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
    img {
      width: 28px;
      height: 28px;
      margin-right: 8px;
    }
  }
}
.mr-10 {
  margin-right: 10px;
}
.search {
  :deep(.ant-col) {
    padding:0 !important;
  }
}
</style>
