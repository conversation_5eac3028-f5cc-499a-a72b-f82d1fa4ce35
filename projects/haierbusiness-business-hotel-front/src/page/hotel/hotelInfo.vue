<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  Input as hInput,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Card as hCard,
} from 'ant-design-vue';
import { SketchOutlined, WhatsAppOutlined, CheckOutlined, CaretUpOutlined, CaretDownOutlined,CheckCircleFilled  } from '@ant-design/icons-vue';
import { computed, onMounted, reactive, ref, watch } from 'vue';
import { useRouter, useRoute, RouteRecordRaw, Router } from 'vue-router';
import dayjs, { Dayjs } from 'dayjs';
// import eFooter from '@haierbusiness-front/components/layout/Footer.vue';
// import eHeader from '@haierbusiness-front/components/layout/Header.vue';
import cityChose from '@haierbusiness-front/components/cityChose/index.vue';
import { hotelListApi } from '@haierbusiness-front/apis';
import { ISmartBrainFilter, ISmartBrain, Datum } from '@haierbusiness-front/common-libs';
import { getCurrentRouter,getCurrentRoute, routerParam } from '@haierbusiness-front/utils';
import aMap from '../components/aMap/index.vue';
import aMapLayout from '../components/aMapLayout/index.vue';
import { api as viewerApi } from 'v-viewer';
const router = getCurrentRouter();
const route = ref(getCurrentRoute());
const mapRef1 = ref();
const mapRef2 = ref();
const roomListLoading = ref<boolean>(false);
const RoomTypeList = ref<any>([]);
const RoomTypeListAll = ref<any>([]);
const dateFormat = 'YYYY-MM-DD';
const todayStart = dayjs().startOf('day').format(dateFormat);
const tomorrowStart = dayjs().add(1, 'day').startOf('day').format(dateFormat);
const SearchForm = ref<any>({
  hotelDate: [todayStart, tomorrowStart],
});
const windowTypeMap = {
  0:'无窗',
  1:'部分有窗',
  2:'有窗', 
  4:'内窗',
  5:'天窗',
  6:'封闭窗',
  7:'飘窗',
  8:'落地窗',
  9:'装饰性假窗',
  10:'窗户较小',
  11:'窗户有墙体或遮挡',
  12:'部分内窗',
  13:'部分天窗',
  14:'部分封闭窗',
  15:'部分窗户较小',
  16:'部分窗外有墙体或遮挡',
  17:'部分装饰性假窗',
  18:'部分飘窗',
  19:'部分落地窗'
}
const searchForm = reactive({
  payType:'b',
  bedType:[],
  cancelRuleType:[],
  roomMealType:[]
})
const lowestPrice = ref<any>(null)
const open = ref<boolean>(false)
const columns = [
  {
    title: '供应商',
    dataIndex: 'supplierName',
  },
  {
    title: '床型',
    dataIndex: 'bedTypeName',
    width: '12%',
    ellipsis:true
  },
  {
    title: '早餐',
    dataIndex: 'roomMealTypeName',
    width: '10%',
  },
  {
    title: '取消规则',
    dataIndex: 'cancelRuleTypeName',
    width: '10%',
  },
  {
    title: '窗户类型',
    dataIndex: 'windowType',
    width: '10%',
  },
  {
    title: '预付/现付',
    dataIndex: 'payment',
    width: '10%',
  },
  {
    title: '首日房费',
    dataIndex: 'firstDayPrice',
    width: '20%',
  },
];
const dataSourceColumns =  [
  {
    title: '酒店名称',
    dataIndex: 'name',

  },
  {
    title: '酒店电话',
    dataIndex: 'phone',

  },
  {
    title: '酒店地址',
    dataIndex: 'address',
  },
    {
    title: '区域',
    dataIndex: 'regionName',
  },
  {
    title: '供应商',
    dataIndex: 'providerCodeName',
  }
];
const roomInfo = ref<any>({})
const lookRoomInfo = (row:any)=>{
  roomInfo.value = row
    open.value = true
}

watch(searchForm,()=>{
  // 筛选床型
  if(searchForm.bedType.length!=0){
      RoomTypeList.value = RoomTypeListAll.value.filter((item:any)=>{
        return searchForm.bedType.includes(String(item.bedType))
      })
  }else{
    RoomTypeList.value = JSON.parse(JSON.stringify(RoomTypeListAll.value))
  }
  // 筛选早餐和取消规则
  if(searchForm.roomMealType.length!=0){
    RoomTypeList.value.forEach((item:any)=>{
          item.roomInfoList = item.roomInfoList?.filter((v:any)=>{
        return searchForm.roomMealType.includes(String(v.roomMealType))
      })
    })
  }
  if(searchForm.cancelRuleType.length!=0){
    RoomTypeList.value.forEach((item:any)=>{
          item.roomInfoList = item.roomInfoList?.filter((v:any)=>{
        return searchForm.cancelRuleType.includes(String(v.cancelRuleType))
      })
    })
  }
  RoomTypeList.value.forEach((item:any)=>{
    item.roomInfoListSix = item.roomInfoList.slice(0,6)
    item.isOpen = false
  })
})
// 酒店信息
const hotel = ref<any>({ hotelDetail:{}});
const hotelLoading = ref<boolean>(true)
const getHotelInfo = async () => {
  const res = await hotelListApi.hotelInfo({ code: route.value.query.code });
  if(res.starLevel == 50){
    res.starLevel=5;
  }else if(res.starLevel == 40){
    res.starLevel=4;
  }else if(res.starLevel == 30){
    res.starLevel=3;
  }else if(res.starLevel == 20){
    res.starLevel=2;
  }else{
    res.starLevel=0;
  }


  hotel.value = res;
  let stringLonLat= getLonLat(res);
   if(stringLonLat.indexOf(",")>0){
    let lonlatArray= stringLonLat.split(",");
    let lon=  parseFloat(lonlatArray[0]);
    let lat=  parseFloat(lonlatArray[1]); 
  mapRef1.value.createByMap(lon, lat);
  mapRef2.value.createByMap(lon, lat);
  setTimeout(() => {
    mapRef1.value.addMarker(lon, lat);
    mapRef1.value.setZoomAndCenter(lon, lat);
    mapRef2.value.addMarker(lon, lat);
    mapRef2.value.setZoomAndCenter(lon, lat);
  }, 1000);
}

  // 遍历获取酒店房型列表
  getAllRoomTypeList()
  setInterval(() => {
    hotelLoading.value = false
  }, 3000);

};

const openChange = (status) =>{
  if(!status&&checkOutDate.value!=SearchForm.value.hotelDate){
    getAllRoomTypeList()
  }
}

const inquiryTips = ref<any>([])
// 查询房型数据
const getAllRoomTypeList=()=>{
  inquiryTips.value = []
  RoomTypeListAll.value = []
  RoomTypeList.value = []
  hotel.value.hotelMappingList.forEach(item=>{
    console.log(item,"供应商")
    // if(item.providerCode!='XC'){
      getRoomTypeList(item.mappingHotelCode,item.providerCode,item.providerCodeName)
    // }
  })
}
const handleClick = (e: any) => {
  e.preventDefault();
};
const getScoreTitle = (score: number) => {
  if (score == 5) {
    return '完美';
  } else if (score == 4.9) {
    return '超棒';
  } else if (score > 4.5 && score <= 4.8) {
    return '很棒';
  } else if (score > 4.0 && score <= 4.5) {
    return '很好';
  } else if (score > 3.0 && score <= 4.0) {
    return '不错';
  } else if (score >= 0 && score <= 3.0) {
    return '一般';
  } else {
    return '一般';
  }
};
const getLonLat = (item: any) => {
    if(item.gdLon && item.gdLat){
      return item.gdLon+","+item.gdLat;
    }
    if(item.gLon && item.gLat){
      return item.gLon+","+item.gLat;
    }
    if(item.bdLon && item.bdLat){
      return item.bdLon+","+item.bdLat;
   }
   return "";
};

const getArrayValue = (value: any) => {
  if(value){
    return value.split(',');
  }else{
    return null
  }
};

// 查看图片
const lookImgUrl = (value: Array<string>): void => {
  console.log(value)
  const imgList:any = []
  value.forEach((v:any)=>{
    imgList.push(v.pictureUrl)
  })
 viewerApi({
    images: imgList,
  });
};

const checkOutDate = ref<any>([])

// 获取 酒店房间列表
const getRoomTypeList = (providerHotelCode:string,providerCode:string,providerCodeName:string) => {
  inquiryTips.value.push(providerCodeName+'正在询价中')
  roomListLoading.value = true;
  checkOutDate.value = SearchForm.value.hotelDate
  hotelListApi.roomTypeList({
    providerHotelCode: providerHotelCode,
    providerCode:providerCode,
    checkOutDate: SearchForm.value.hotelDate[1],
    checkInDate: SearchForm.value.hotelDate[0],
  }).then((res)=>{
    inquiryTips.value.push(providerCodeName+'询价完成')
    // 判断是否为空 第一个请求回来的直接push
    if(RoomTypeList.value&&RoomTypeList.value.length==0){
        RoomTypeList.value = res;
    }else{
      res.forEach((item:any)=>{
        const bobIndex =  RoomTypeList.value.findIndex(v => v.baseRoomName == item.baseRoomName);
        if(bobIndex!=-1){
          RoomTypeList.value[bobIndex].roomInfoList.push(...item.roomInfoList)
        }else{
          RoomTypeList.value.push(item)
        }
      })
    }
    RoomTypeList.value.forEach((item:any)=>{
      item.roomInfoListSix = item.roomInfoList.slice(0,6)
      item.isOpen = false
      // 获取最低价
      item.roomInfoList.forEach((v:any)=>{
        if(v.firstDayPrice<=lowestPrice.value||!lowestPrice.value){
          lowestPrice.value = v.firstDayPrice
        }
      })
      // 价格由低到高排序
      item.roomInfoList.sort(function(a, b) {
        return a.firstDayPrice - b.firstDayPrice;
      });
      item.roomInfoListSix = item.roomInfoList.slice(0,6)
    })
    // 如果询价完成提前关闭loading
    if(inquiryTips.value?.length==hotel.value.hotelMappingList.length*2){
      hotelLoading.value = false
    }
    RoomTypeListAll.value = RoomTypeList.value;
    roomListLoading.value = false;
  })
  .catch(()=>{
    inquiryTips.value.push(providerCodeName+'询价完成')
    roomListLoading.value = false;
  })
};
const handleMap = () =>{
   const dom = document.getElementById(`transportationLocation`)
  dom && dom.scrollIntoView({ behavior: 'instant',block:'end' , inline: "end"})
}
const gotoList = () =>{
  router.push({
    path: '/hotel-analysis/list'
  });
}

onMounted(() => {
  if(route.value.query.date){
    SearchForm.value.hotelDate = JSON.parse(route.value.query.date)
  }
  getHotelInfo();
  // getRoomTypeList();
});
</script>

<template>
<div class="homeBox">
  <div class="contentBox">
    <div class="breadcrumb">
      <a-breadcrumb>
        <a-breadcrumb-item><a @click="gotoList" style="color:#1677ff">酒店列表</a></a-breadcrumb-item>
        <a-breadcrumb-item>酒店详情</a-breadcrumb-item>
      </a-breadcrumb>
    </div>
    <div class="hotelInfoBox">
      <div class="topBox">
        <div class="top_left">
          <div class="hotelName">
            {{ hotel.name
            }}<SketchOutlined v-for="v in hotel.starLevel" :style="{ fontSize: '14px', color: '#ff6600' }"></SketchOutlined>
          </div>
          <div class="address">【{{ hotel.regionName }}】{{ hotel.address }} <a @click="handleMap">查看地图</a></div>
        </div>
        <div class="topRight fr">
          <div v-if="lowestPrice"><em>¥</em> <strong id="szdj">{{lowestPrice}}</strong> <span>起</span></div>
        </div>
      </div>
      <div class="bottomBox">
        <div class="bottomLeft">
          <div class="leftImgBox">
            <img v-if="hotel?.hotelPictures"  v-lazy="hotel?.hotelPictures&&hotel?.hotelPictures.length&&hotel?.hotelPictures[0].pictureUrl" alt="" />
            <div @click="lookImgUrl(hotel?.hotelPictures)" v-if="hotel?.hotelPictures" class="detail-headalbum_focus_des">
              查看所有{{ hotel?.hotelPictures.length }}图片
            </div>
          </div>
          <div class="rightImgBox">
            <div v-for="(item, index) in hotel?.hotelPictures" class="imgBox">
              <img v-lazy="item.pictureUrl" alt="" />
            </div>
          </div>
        </div>
        <div class="bottomReft">
          <div>
            <p class="score" v-if="hotel.hotelDetail.commentScoreTotal">
              <span>{{ hotel.hotelDetail.commentScoreTotal }} </span>分
            </p>
            <p class="score" v-else><span>暂无评分</span></p>
            <span v-if="hotel.hotelDetail.commentScoreTotal" class="scoreTitle">{{ getScoreTitle(hotel.hotelDetail.commentScoreTotal) }}</span>
            <a-tooltip placement="topLeft" :title="hotel.hotelDetail.bestCommentSentence">
              <div class="bestCommentSentence ellipsis">{{ hotel.hotelDetail.bestCommentSentence }}</div>
            </a-tooltip>
            <a-popover>
                <template #content>
                  <div style="width:300px;" v-html='hotel.hotelDetail.description'></div>
                </template>
              <div class="description ellipsis">{{ hotel.hotelDetail.description }}</div>
            </a-popover>
            <a-tooltip placement="topLeft" :title="hotel.phone">
              <div class="phone ellipsis"><WhatsAppOutlined /> {{ hotel.phone }}</div>
            </a-tooltip>
            <a-tooltip placement="topLeft" :title="hotel.hotelDetail.arrivalDeparturePolicy">
                <div class="arrivalDeparturePolicy ellipsis">{{ hotel.hotelDetail.arrivalDeparturePolicy }}</div>
            </a-tooltip>
          </div>
          <div class="mapBox">
            <aMap :key="1" ref="mapRef1"></aMap>
            <div @click="handleMap" class="lookMap">查看地图</div>
          </div>
        </div>
      </div>
    </div>
    <div class="hotelRoomContent">
      <div class="anchorBox">
        <a-anchor
          affix
          :target-offset="200"
          :bounds="200"
          @click="handleClick"
          :offsetTop="2"
          direction="horizontal"
          :items="[
            {
              key: '7',
              href: '#dataSource',
              title: '数据来源',
            },
            {
              key: '1',
              href: '#roomTypePrice',
              title: '房型价格',
            },
            {
              key: '2',
              href: '#hotelInformation',
              title: '酒店信息',
            },
            {
              key: '3',
              href: '#hotelPolicies',
              title: '酒店政策',
            },
            {
              key: '4',
              href: '#serviceFacilities',
              title: '服务设施',
            },
            {
              key: '5',
              href: '#transportationLocation',
              title: '交通位置',
            },
            {
              key: '6',
              href: '#guestReviews',
              title: '住客点评',
            }
          ]"
        />
        <div class="hotelDateBox">
          <a-range-picker @openChange="openChange" style="width: 600px" v-model:value="SearchForm.hotelDate" valueFormat="YYYY-MM-DD" />
          <div class="loading">
            <!-- 当前询价中 -->
            <span v-if="hotel.hotelMappingList&&(inquiryTips?.length==hotel.hotelMappingList.length*2)">
              <CheckCircleFilled  style="margin-right:5px;color:#389e0d;font-size:16px;" /> 当前询价已全部完成
            </span>
            <span v-else>
              <a-spin size="small"  style="margin-right:8px" />{{ inquiryTips[inquiryTips.length-1] }}
            </span>
          </div>
        </div>
        <div class="filterBox">
          <!-- <div>
            <span>支付方式 </span>
            <a-checkbox-group v-model:value="searchForm.payType" button-style="solid">
              <a-checkbox value="a">到店付</a-checkbox>
              <a-checkbox value="b">在线付</a-checkbox>
            </a-checkbox-group>
          </div> -->
          <div>
            <span>床型</span>
            <a-checkbox-group v-model:value="searchForm.bedType" button-style="solid">
              <a-checkbox value="1">大床</a-checkbox>
              <a-checkbox value="2">双床</a-checkbox>
              <a-checkbox value="3">其他</a-checkbox>
            </a-checkbox-group>
          </div>
          <div>
            <span>早餐</span>
            <a-checkbox-group v-model:value="searchForm.roomMealType" button-style="solid">
              <a-checkbox value="1">无早</a-checkbox>
              <a-checkbox value="2">单早</a-checkbox>
              <a-checkbox value="3">双早</a-checkbox>
              <a-checkbox value="4">三早</a-checkbox>
            </a-checkbox-group>
          </div>
          <div>
            <span>取消规则 </span>
            <a-checkbox-group v-model:value="searchForm.cancelRuleType" button-style="solid">
              <a-checkbox value="1">免费取消</a-checkbox>
              <a-checkbox value="2">限时取消</a-checkbox>
              <a-checkbox value="3">不可取消</a-checkbox>
            </a-checkbox-group>
          </div>
        </div>
      </div>
      <div  class="hotelInformation infoBox" id="dataSource">
        <div class="title">数据来源</div>
          <h-table :pagination="false" :columns="dataSourceColumns" :data-source="hotel.originalHotelList" bordered>
          </h-table>
      </div>
      <div class="roomTypePrice infoBox" id="roomTypePrice">
        <div class="title">房型价格</div>
        <div v-if="roomListLoading||hotelLoading" style="width: 100%; text-align: center">
          <a-spin />
        </div>
        <div v-else-if="RoomTypeList&&!RoomTypeList.length">
            <a-empty description="暂无价格数据" />
        </div>
        <div v-else>
          <div v-for="item in RoomTypeList" v-show="item?.roomInfoList&&item?.roomInfoList?.length" class="hotelList">
            <div class="leftBox">
              <div @click="lookRoomInfo(item)" class="imgBox">
                <img v-lazy="item.roomImageUrlList&&item.roomImageUrlList.length?item.roomImageUrlList[0]:null" alt="" />
                <!-- <div class="noImg" v-else>暂无图片</div> -->
              </div>
              <p class="roomType">{{item.baseRoomName}}</p>
              <p class="label">
                面积：<span>{{ item.roomArea }}</span> 楼层：<span>{{ item.floor }}</span>
              </p>
              <!-- <p class="label">
                推荐入住人数: <span>{{ item.bedType }}</span>
              </p> -->
              <p class="label">
                床型: <span>{{ item.bedTypeName }}</span>
              </p>
              <p @click="lookRoomInfo(item)" class="roomInfo">查看详情</p>
            </div>
            <div class="rightBox">
              <div class="num">共计<span>{{item.roomInfoList?.length}}</span>个报价</div>
              <h-table :pagination="false" :columns="columns" :data-source="!item.isOpen?item.roomInfoListSix:item.roomInfoList" bordered>
                <template #bodyCell="{ column, text, record,index }">
                  <template v-if="column.dataIndex === 'supplierName'">
                    <div>
                      <span style="width:60px;display:inline-block;">{{ text }}</span>
                      <span style="margin-top:6px;">
                        <a-tag :bordered="false"  color="blue" v-if="record.bookMinRoomNumber && record.bookMinRoomNumber>1">{{ record.bookMinRoomNumber }}间起订</a-tag >
                        <a-tag :bordered="false" color="blue" v-if="record.bookMaxRoomNumber && record.bookMaxRoomNumber < 9">最多定{{ record.bookMaxRoomNumber }}间</a-tag >
                        <a-tag :bordered="false" color="blue" v-if="record.bookMinDay && record.bookMinDay>1">{{ record.bookMinDay }}天起订</a-tag >
                        <a-tag :bordered="false" color="blue" v-if="record.bookMaxDay && record.bookMaxDay < 30">最多定{{ record.bookMaxDay }}天</a-tag>
                      </span>
                    </div>
                  </template>
                  <template v-if="column.dataIndex === 'payment'">
                    <a-tag :color="record.payment==0?'cyan':'orange'" v-if="record.payment||record.payment==0">
                      {{ record.payment==0?'现付':'预付' }}
                    </a-tag>
                  </template>
                  <template v-if="column.dataIndex === 'windowType'">
                    <a-popover>
                      <template #content>
                        <p style="width:300px;">{{ record.windowTypeDesc  }}</p>
                      </template>
                      <a-button type="link">{{ record.windowTypeName}}</a-button>
                    </a-popover>
                  </template>
                  <template v-if="column.dataIndex === 'cancelRuleTypeName'">
                    <a-popover>
                      <template #content>
                        <p style="width:300px;">{{ record.cancelRuleTypeDesc }}</p>
                      </template>
                      <a-button type="link">{{ record.cancelRuleTypeName }}</a-button>
                    </a-popover>
                  </template>
                  <template v-if="column.dataIndex === 'firstDayPrice'">
                    <a-popover>
                      <template #content>
                        <div class="Pricebox">
                            <div class="topTitle">
                                <div>您已选择{{SearchForm.hotelDate[0]}}至{{SearchForm.hotelDate[1]}}共{{record.dayPriceList.length}}晚 </div>  <div>币种：人民币</div>
                            </div>
                            <div class="somePricebox">
                                <p class="item" v-for="v in record.dayPriceList">   
                                    <span> {{v.date.substring(0, 10)}}</span>
                                    <div class="firstDayPrice"><em>¥</em>{{ v.price }}</div>
                                </p>
                            </div>
                        </div>
                      </template>
                      <div class="firstDayPrice"><em>¥</em>{{ record.firstDayPrice }}
                        <!-- <span style="font-size:12px;color:#06bd27;font-weight:400;"  v-if="record.quotaNumShow!='房量充足'">
                          {{record.quotaNumShow }}
                        </span> -->
                        <span style="font-size:12px;color:#ff5522;font-weight:400;"  v-if="record.quotaNumShow!='房量充足'">
                          {{record.quotaNumShow }}
                        </span>
                        </div>
                      <div>
                        <!-- <a-tag v-if="record.roomStatus==0" color="green">
                          充足
                        </a-tag> 
                        <a-tag v-if="record.roomStatus==1" color="red">
                          紧张
                        </a-tag> 
                        <a-tag v-if="record.roomStatus==2" color="red">
                          满房
                        </a-tag>  -->
                      </div>
                    </a-popover>
                  </template>
                  <template v-if="column.dataIndex === 'roomMealTypeName'">
                    <a-popover>
                      <template #content>
                        <div class="Pricebox">
                            <div class="somePricebox">
                                <p class="item" v-for="v in record.roomMealTypeDetailList">   
                                    <span> {{v.effectDate.substring(0, 10)}}</span>
                                    <div>{{ v.dailyMealInfo[0]?v.dailyMealInfo[0]:'无早' }}</div>
                                </p>
                            </div>
                        </div>
                      </template>
                        <a-button type="link">{{ record.roomMealTypeName }}</a-button>
                    </a-popover>
                  </template>
                </template>
              </h-table>
              <div class="openBox" v-if="item.roomInfoList.length>6">
                <div v-if="!item.isOpen"  @click="item.isOpen = true">查看其他<a>{{item.roomInfoList&&item.roomInfoList.length-6}}</a>个报价 <CaretDownOutlined /></div>
                <div v-else  @click="item.isOpen = false">收起<CaretUpOutlined /></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="hotelInformation infoBox" id="hotelInformation">
        <div class="title">酒店信息</div>
        <ul class="info clearfix" id="hotelInfo">
          <li class="jdxjIconFont"  v-if="hotel.starLevel && hotel.startLevel!=0">
            <div class="left">酒店星级</div>
            <div class="right" style="color: #f60">
              <SketchOutlined v-for="v in hotel.starLevel" :style="{ fontSize: '14px', color: '#ff6600' }"></SketchOutlined>
            </div>
          </li>
          <li v-if="hotel.openDate || hotel.decorationDate">
            <div class="left">基本信息</div>
            <div class="right"  v-if="hotel.openDate">{{ hotel.decorationDate }}开业&nbsp;&nbsp;</div>
            <div class="right">{{ hotel.decorationDate }}装修</div>
          </li>
          <li  v-if="hotel.phone">
            <div class="left">联系方式</div>
            <div class="right" id="jddh" jddh="0532-66968888">{{ hotel.phone }}</div>
          </li>
          <li  v-if="hotel.address">
            <div class="left">酒店地址</div>
            <div class="right">{{ hotel.address }}</div>
          </li>
          <li  v-if="hotel.hotelDetail.description">
            <div class="left">酒店简介</div>
            <div v-html="hotel.hotelDetail.description" class="right">
            </div>
          </li>
        </ul>
      </div>
      <div class="hotelInformation infoBox" id="hotelPolicies">
        <div class="title">酒店政策</div>
        <ul class="info clearfix" id="hotelInfo">
          <li v-if="hotel.hotelDetail.arrivalDeparturePolicy" class="jdxjIconFont">
            <div class="left">入离时间</div>
            <div class="right" style="color: #f60">
              {{ hotel.hotelDetail.arrivalDeparturePolicy }}
            </div>
          </li>
          <li  v-if="hotel.hotelDetail.mealPolicy" >
            <div class="left">早餐信息</div>
            <div class="right" v-html="hotel.hotelDetail.mealPolicy"></div>
          </li>
          <li  v-if="hotel.hotelDetail.childAndAddBedPolicy" >
            <div class="left">儿童和加床政策</div>
            <div class="right" v-html="hotel.hotelDetail.childAndAddBedPolicy"></div>
          </li>

          <li  v-if="hotel.hotelDetail.ageLimitPolicy" >
            <div class="left">年龄限制政策</div>
            <div class="right">{{ hotel.hotelDetail.ageLimitPolicy }}</div>
          </li>
          <li  v-if="hotel.hotelDetail.checkLimitInfoDescription" >
            <div class="left">入住提示</div>
            <div class="right">{{ hotel.hotelDetail.checkLimitInfoDescription }}</div>
          </li>
          <li  v-if="hotel.hotelDetail.paymentMethod" >
            <div class="left">前台可用支付方式</div>
            <div class="right">
              {{ hotel.hotelDetail.paymentMethod }}
            </div>
          </li>
          <li  v-if="hotel.hotelDetail.importantNotice" >
            <div class="left">重要通知(提示)</div>
            <div class="right" v-html="hotel.hotelDetail.importantNotice"></div>
          </li>
        </ul>
      </div>
      <div class="serviceFacilities infoBox" id="serviceFacilities">
        <div class="title">服务设施</div>
        <div class="serviceFacilitiesBox">
          <div v-for="item in hotel?.hotelFacilities" class="serviceFacilitiesItem">
            <div class="type">{{ item.name }}</div>
            <div class="facilitiesItem">
              <div class="facilitiesItemBox" v-for="v in getArrayValue(item.value)">
                <CheckOutlined :style="{ marginRight: '3px' }" />{{ v }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="transportationLocation infoBox" id="transportationLocation">
        <div class="title">交通位置</div>
        <div class="mapBox">
          <aMapLayout :key="2" ref="mapRef2"></aMapLayout>
        </div>
      </div>
      <div class="hotelInformation infoBox" id="guestReviews">
        <div class="title">住客点评</div>
        <ul class="info clearfix" id="hotelInfo">
          <li class="jdxjIconFont">
            <div class="left">总体评价</div>
            <div class="right" style="color: #f60">
              <a-rate disabled v-model:value="hotel.hotelDetail.commentScoreTotal" allow-half />
            </div>
          </li>
          <li>
            <div class="left">地理位置</div>
            <div class="right"><a-rate disabled v-model:value="hotel.hotelDetail.commentScoreLocation" allow-half /></div>
          </li>
          <li>
            <div class="left">服务</div>
            <div class="right"><a-rate disabled v-model:value="hotel.hotelDetail.commentScoreService" allow-half /></div>
          </li>
          <li>
            <div class="left">设施</div>
            <div class="right"><a-rate disabled v-model:value="hotel.hotelDetail.commentScoreFacility" allow-half /></div>
          </li>
          <li>
            <div class="left">卫生</div>
            <div class="right"><a-rate disabled v-model:value="hotel.hotelDetail.commentScoreCleanliness" allow-half /></div>
          </li>
          <li>
            <div class="left">点评短句</div>
            <div class="right">
              {{ hotel.hotelDetail.bestCommentSentence }}
            </div>
          </li>
        </ul>
      </div>
    </div>
    <!-- 房型详情 -->
    <a-modal :footer="null" width="800px" v-model:open="open" title="房型详情">
      <a-carousel autoplay arrows dots-class="slick-dots slick-thumb">
        <template #customPaging="props">
          <a>
            <img v-lazy="roomInfo.roomImageUrlList[props.i]" />
          </a>
        </template>
        <div v-for="item in roomInfo.roomImageUrlList" :key="item">
          <img v-lazy="item" />
        </div>
      </a-carousel>
      <div class="roomInfoBox">
        <p class="label">
          面积：<span>{{ roomInfo.roomArea }}</span> 楼层：<span>{{ roomInfo.floor }}</span> 床型: <span>{{ roomInfo.bedTypeName }}</span>
        </p>
        <!-- <p class="label">
          推荐入住人数: <span>{{ roomInfo.bedType }}</span>
        </p> -->
        <div v-if="roomInfo.roomFaclitiesList" class="jcssBox">
          <div class="label title">基础设施：</div>
          <div class="label"> 
            <span v-for="v in roomInfo.roomFaclitiesList">
              {{v}}
            </span>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</div>
</template>

<style lang="less" scoped>
.homeBox{
    width: 100%;
     background: #f5f7fa;
}
.contentBox {
padding-bottom: 24px;
  width: 1280px;
  margin: 0 auto;
  .breadcrumb {
    padding: 20px 0;
  }
}

.hotelInfoBox {
  background-color: #fff;
  padding: 16px 24px;
  margin-bottom: 8px;
  .topBox {
    display: flex;
    justify-content: space-between;
    .top_left {
      .hotelName {
        font-size: 20px;
        color: #0f294d;
        display: inline;
        margin-right: 8px;
        font-weight: bolder;
        width: 100%;
      }
      .address {
        width: 100%;
        font-size: 14px;
        color: #455873;
        letter-spacing: 0;
        text-align: left;
        line-height: 14px;
        margin-top: 14px;
        display: inline-block;
        a {
          cursor: pointer;
          color: #008ad2 !important;
        }
      }
    }
    .topRight {
      float: right;
      display: flex;
      align-items: center;
      em {
        font-size: 24px;
        color: #ff5522;
      }
      strong {
        font-weight: bold;
        font-size: 24px;
        color: #ff5522;
      }
    }
  }
  .bottomBox {
    display: flex;
    justify-content: space-between;
    margin-top: 12px;
    .bottomLeft {
      display: flex;
      .leftImgBox {
        width: 475px;
        height: 302px;
        display: inline-block;
        cursor: pointer;
        position: relative;
        img {
          width: 100%;
          height: 100%;
        }
        .detail-headalbum_focus_des {
          position: absolute;
          right: 16px;
          bottom: 14px;
          color: #fff;
          font-size: 14px;
          font-weight: 700;
        }
        // background: green;
      }
      .rightImgBox {
        width: 400px;
        height: 302px;
        display: flex;
        flex-wrap: wrap;
        overflow: hidden;
        .imgBox {
          width: 190px;
          height: 147px;
          margin: 0 0 8px 8px;
          img {
            width: 100%;
            height: 100%;
          }
        }
      }
    }
    .bottomReft {
      display: flex;
      flex-flow: column;
      justify-content: space-between;
      .score {
        background-color: #4978ce;
        padding: 3px 8px;
        border-radius: 2px;
        font-size: 22px;
        line-height: 22px;
        color: hsla(0, 0%, 100%, 0.6);
        cursor: pointer;
        white-space: nowrap;
        display: inline-block;
        font-size: 14px;
        span {
          font-size: 16px;
          font-weight: 700;
          color: #fff;
          margin-right: 3px;
        }
      }
      .scoreTitle {
        color: #4978ce;
        font-size: 20px;
        margin-left: 8px;
        font-weight: 700;
        position: relative;
        top: 1px;
      }
      .bestCommentSentence {
        margin-bottom: 8px;
        font-size: 14px;
        color: #06aebd;
        font-weight: 400;
        white-space: nowrap;
      }
      .description {
        margin-bottom: 8px;
        font-size: 14px;
        color: #06aebd;
        font-weight: 400;
        white-space: nowrap;
        width: 350px;
      }
      .phone {
        margin-bottom: 8px;
        font-size: 14px;
        color: #06aebd;
        font-weight: 400;
        white-space: nowrap;
      }
      .arrivalDeparturePolicy {
        margin-bottom: 8px;
        font-size: 14px;
        color: #eb17c7;
        font-weight: 400;
        white-space: nowrap;
      }
      .mapBox {
        width: 350px;
        height: 160px;
        position: relative;
        .lookMap {
          position: absolute;
          right: 0;
          bottom: 0;
          outline: 0;
          border: none;
          border-radius: 1px;
          cursor: pointer;
          width: 100px;
          height: 30px;
          color: #fff;
          text-align: center;
          line-height: 30px;
          background: #008ad2;
          font-size: 14px;
        }
      }
    }
  }
}
  .roomInfoBox{
    margin-top:10px;
    .jcssBox{
      display: flex;
      .title{
        width:60px;
        flex-shrink: 0;
      }
    }
    .label {
          margin-bottom: 4px;
          color: #999999;
          font-size: 12px;
          span {
            margin-left: 3px;
            margin-right: 6px;
            color: #000;
          }
        }
        p {
          margin: 0;
          padding: 0;
        }
  }
.hotelRoomContent {
  position: relative;
  .infoBox {
    margin-top: 10px;
    padding: 20px 30px 30px;
    border-radius: 4px;
    box-shadow: 0 0 10px #f5f4f4;
    background: #ffffff;
    .title {
      font-size: 18px;
      color: #0f294d;
      line-height: 24px;
      font-weight: 900;
      padding-bottom: 16px;
    }
  }
  .roomTypePrice {
    .hotelList {
      display: flex;
      padding: 20px 6px 20px 6px;
      border-bottom: 1px solid #dfdfdf;
      .leftBox {
        width: 200px;
        flex-shrink: 0;
        .imgBox {
          width: 180px;
          height: 150px;
          cursor: pointer;
          img {
            width: 100%;
            height: 100%;
          }
          .noImg {
            width: 100%;
            height: 100%;
            background: #f8f6f6;
            color: #333;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
        p {
          margin: 0;
          padding: 0;
        }
        .roomInfo{
          color: #008ad2;
          font-size: 12px;
          padding-top:3px;
          cursor: pointer;
        }
        .roomType {
          font-size: 18px;
          font-weight: bold;
          margin-top: 5px;
          margin-bottom: 10px;
        }
        .label {
          margin-bottom: 4px;
          color: #999999;
          font-size: 12px;
          span {
            margin-left: 10px;
            color: #000;
          }
        }
      }
      .rightBox {
        flex: 1;
        .openBox{
           text-align: right;
          font-size: 14px;
          margin-top: 13px;
          cursor: pointer;
          a{
            color: #ff5522;
          }
        }
        .num{
          text-align: right;
          font-size: 14px;
          margin-bottom: 10px;
          span{
            color: #ff5522;
          }
        }
        .firstDayPrice {
          em {
            font-size: 16px;
            margin-right: 3px;
          }
          font-size: 18px;
          font-weight: bold;
          color: #ff5522;
          // width: 80px;
        }
      }
    }
  }
  .transportationLocation {
    .mapBox {
      width: 100%;
      height: 600px;
    }
  }
  .serviceFacilities {
    .serviceFacilitiesBox {
      padding: 20px;
      .serviceFacilitiesItem {
        display: flex;
        padding-bottom: 20px;
        .type {
          width: 150px;
          font-size: 14px;
          color: #0f294d;
          letter-spacing: 0;
          line-height: 18px;
          font-weight: bold;
          flex-shrink: 0;
        }
        .facilitiesItem {
          display: flex;
          flex-wrap: wrap;
          .facilitiesItemBox {
            font-size: 14px;
            color: #0f294d;
            letter-spacing: 0;
            line-height: 18px;
            margin-right: 10px;
            width: 140px;
            margin-bottom: 10px;
          }
        }
      }
    }
  }
  .hotelInformation {
    ul li {
      display: flex;
      padding: 8px 0;
    }
    .left {
      font-size: 14px;
      color: #0f294d;
      letter-spacing: 0;
      line-height: 18px;
      font-weight: bold;
      flex-shrink: 0;
      margin-right: 10px;
      padding-left: 20px;
      width: 160px;
    }
    .right {
      font-size: 14px;
      color: #0f294d;
      letter-spacing: 0;
      line-height: 18px;
    }
  }
  .anchorBox {
    position: sticky;
    top: 0; /* 离顶部的距离 */
    z-index: 100;
    background: #fff;
    padding: 10px 10px 20px 10px;
    .hotelDate {
      margin-top: 20px;
      width: 500px;
    }
    .filterBox {
      display: flex;
      margin-top: 20px;
      > div {
        margin-right: 24px;
      }
      span {
        font-size: 14px;
        color: #999999;
        padding: 0 5px;
        margin-right: 5px;
      }
    }
  }
}
:deep(.ant-anchor .ant-anchor-link) {
  padding: 0 0 0 50px;
}
:deep(.ant-anchor .ant-anchor-link-title) {
  padding-bottom: 8px;
}

.ellipsis {
  white-space: nowrap; /* 确保文本在一行内显示 */
  overflow: hidden; /* 隐藏超出容器的文本 */
  text-overflow: ellipsis; /* 使用省略符号表示被截断的文本 */
}
.Pricebox{
    width: 400px;
    padding: 6px;
    .topTitle{
        display: flex;
        justify-content: space-between;
    }
    .somePricebox{
        margin-top:10px;
        .item{
            display: flex;
            span{
                margin-right: 20px;
                font-weight: 600;
            }
                .firstDayPrice {
          em {
            font-size: 12px;
            margin-right: 3px;
          }
          font-size: 14px;
          font-weight: bold;
          color: #ff5522;
          width: 80px;
        }
        }
    }
}
:deep(.slick-list){
  height: 360px;
}
:deep(.slick-dots) {
  position: relative;
  height: auto;
}
:deep(.slick-slide img) {
  border: 5px solid #fff;
  display: block;
  margin: auto;
  width:400px;
  height: 340px;
}
:deep(.slick-arrow) {
  display: none !important;
}
:deep(.slick-thumb) {
  bottom: 0px;
}
:deep(.slick-thumb li) {
  width: 60px;
  height: 45px;
}
:deep(.slick-thumb li img) {
  width: 100%;
  height: 100%;
  filter: grayscale(100%);
  display: block;
}
:deep .slick-thumb li.slick-active img {
  filter: grayscale(0%);
}
:deep(.ant-table-cell){
  padding:6px 16px!important;
}
:deep(.ant-empty-description){
  color: #969595;
}

img{
  object-fit: cover
}
.hotelDateBox{
  margin-top: 20px;
  display: flex;
  .loading{
    flex:1;
    font-size:14px;
    margin-top:5px;
    margin-left:20px;
  }
}
</style>
