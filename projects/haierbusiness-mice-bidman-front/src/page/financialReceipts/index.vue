<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Modal as hModal,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Input as hInput,
  Upload as hUpload,
  message,
  Modal,
} from 'ant-design-vue';
import { SearchOutlined } from '@ant-design/icons-vue';
import { financialReceiptsApi } from '@haierbusiness-front/apis';
import {
  IFinancialReceiptsFilter,
} from '@haierbusiness-front/common-libs';
import { ref, onMounted, reactive } from 'vue';
import type { Dayjs } from 'dayjs';
import type { UnwrapRef } from 'vue';
import router from '../../router'
import { cloneDeep } from 'lodash';

const currentRouter = ref()
type RangeValue = [Dayjs, Dayjs];
const value2 = ref<RangeValue>();
//弹出框
const open = ref<boolean>(false);

const handleOk = (e: MouseEvent) => {
  console.log(e);
  open.value = false;
};
const handleSelect = () => {
  open.value = true;
}

const reset = () => {
  // 重置表单
  value2.value = undefined;
  // 可以添加其他需要重置的字段
};

const columns = [
  {
    title: '收款单号',
    dataIndex: 'name',
    width: '180px',
  },
  {
    title: '付款单位',
    dataIndex: 'age',
    width: '180px',
  },
  {
    title: '付款时间',
    dataIndex: 'address',
    width: '180px',
  },
  {
    title: '付款金额',
    dataIndex: 'address',
    width: '180px',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: '180px',
  },
];
interface DataItem {
  key: string;
  name: string;
  age: number;
  address: string;
}
const data: DataItem[] = [];

const dataSource = ref(data);
const editableData: UnwrapRef<Record<string, DataItem>> = reactive({});

const edit = (key: string) => {
  editableData[key] = cloneDeep(dataSource.value.filter(item => key === item.key)[0]);
};
const save = (key: string) => {
  Object.assign(dataSource.value.filter(item => key === item.key)[0], editableData[key]);
  delete editableData[key];
};
const cancel = (key: string) => {
  delete editableData[key];
};

//弹出框结束
onMounted(async () => {
  currentRouter.value = await router
})

const searchParam = ref<IFinancialReceiptsFilter>({})

// 收款确认相关操作
const handleConfirmSubmit = async () => {
  try {
    // 模拟提交确认操作
    console.log('提交收款确认');

    hModal.success({
      title: '收款确认成功',
      content: '已成功确认收款信息'
    });
  } catch (error) {
    console.error('收款确认失败:', error);
    hModal.error({
      title: '操作失败',
      content: '收款确认失败，请重试'
    });
  }
};
</script>

<template>
  <div class="receipt-confirm-container">
    <div class="content-wrapper">
      <div class="meeting-info-header">
        <h2 class="page-title">收款确认</h2>
      </div>

      <div class="meeting-basic-info">
        <h3>会议基本信息</h3>
        <h-row :gutter="24">
          <h-col :span="6">
            <div class="info-item">
              <div class="label">会议单号：</div>
              <div class="value">RC20241017101911000001</div>
            </div>
          </h-col>
          <h-col :span="6">
            <div class="info-item">
              <div class="label">会议名称：</div>
              <div class="value">创新互联年会</div>
            </div>
          </h-col>
          <h-col :span="6">
            <div class="info-item">
              <div class="label">会议类型：</div>
              <div class="value">开盘会</div>
            </div>
          </h-col>
          <h-col :span="6">
            <div class="info-item">
              <div class="label">会议人数：</div>
              <div class="value">120</div>
            </div>
          </h-col>
        </h-row>
        <h-row :gutter="24">
          <h-col :span="6">
            <div class="info-item">
              <div class="label">会议时间：</div>
              <div class="value">2025.1.1-2025.1.10</div>
            </div>
          </h-col>
          <h-col :span="18">
            <div class="info-item">
              <div class="label">酒店需求：</div>
              <div class="value">*****酒店附近</div>
            </div>
          </h-col>
        </h-row>
        <h-row :gutter="24">
          <h-col :span="24">
            <div class="info-item">
              <div class="label">备注：</div>
              <div class="value">这是一条备注。这是一条备注。这是一条备注。这是一条备注。这是一条备注。</div>
            </div>
          </h-col>
        </h-row>
      </div>

      <div class="receipt-confirm-section">
        <h3>收款确认</h3>
        <h-row :gutter="24">
          <h-col :span="24">
            <div class="info-item">
              <div class="label">收款记录：</div>
              <h-button type="primary" class="select-btn" @click="handleSelect()">
                <SearchOutlined />选择收款记录
              </h-button>
            </div>
          </h-col>
        </h-row>
      </div>
    </div>

    <!-- 底部固定确认按钮 -->
    <div class="footer-actions">
      <h-button type="primary" class="action-btn" @click="handleConfirmSubmit">确认</h-button>
    </div>
  </div>
  <!-- 选择收款弹出框 -->
  <a-modal centered class="modal" v-model:open="open" width="50.5%" title="选择收款记录" @ok="handleOk">
    <h-row :align="'middle'">
      <h-col :span="2">
        <label style="width: 100%; display: inline-block; text-align: right;" class="modify"
          for="platformHotelName">收款单位：</label>
      </h-col>
      <h-col :span="5">
        <h-input class="input" id="platformHotelName" placeholder="请输入酒店名称" allow-clear />
      </h-col>
      <h-col :span="3" style="text-align: right;padding-left: 0px;">
        <label for="platformHotelName">收款时间：</label>
      </h-col>
      <h-col :span="7">
        <a-range-picker v-model:value="value2" show-time />
      </h-col>
      <h-col :span="7" class="seven">
        <h-button type="primary" style="margin-left: 17%;width: 34.5%;">
          查询
        </h-button>
        <h-button @click="reset" style="margin-left: 8%;width: 34.5%;">重置</h-button>
      </h-col>
    </h-row>
    <!-- 表格 -->
    <a-table :columns="columns" :data-source="dataSource" bordered>
      <template #bodyCell="{ column, text, record }">
        <template v-if="['name', 'age', 'address'].includes(column.dataIndex)">
        </template>
        <template v-else-if="column.dataIndex === 'operation'">
          <div class="editable-row-operations">
            <span v-if="editableData[record.key]">
              <a-typography-link @click="save(record.key)">Save</a-typography-link>
              <a-popconfirm title="Sure to cancel?" @confirm="cancel(record.key)">
                <a>Cancel</a>
              </a-popconfirm>
            </span>
            <span v-else>
              <a @click="edit(record.key)">Edit</a>
            </span>
          </div>
        </template>
      </template>
    </a-table>
  </a-modal>
</template>

<style scoped lang="less">
.receipt-confirm-container {
  position: relative;
  background-color: #fff;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.content-wrapper {
  flex: 1;
  padding: 10px 20px;
  overflow: auto;
}

.meeting-info-header {
  margin-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 10px;
}

.page-title {
  font-size: 20px;
  font-weight: 500;
  margin: 0;
}

.meeting-basic-info,
.receipt-confirm-section {
  margin-bottom: 24px;
  background-color: #fafafa;
  padding: 16px;
  border-radius: 4px;

  h3 {
    margin-top: 0;
    margin-bottom: 16px;
    font-size: 16px;
    color: #333;
    font-weight: 500;
  }
}

.info-item {
  display: flex;
  margin-bottom: 12px;

  .label {
    width: 100px;
    color: #666;
    text-align: right;
    padding-right: 8px;
  }

  .value {
    flex: 1;
    color: #333;
  }
}

.select-btn {
  margin-left: 8px;
}

.footer-actions {
  height: 56px;
  background-color: #fff;
  box-shadow: 0 -2px 6px rgba(0, 0, 0, 0.1);
  padding: 10px 24px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  z-index: 10;
}

.action-btn {
  min-width: 80px;
  margin-left: 12px;
}

.row-container {
  width: 100%;

  .text-right {
    display: flex;
    justify-content: center;

    .margin-right {
      margin-left: 20px;
    }
  }
}

#examineCode {
  display: inline-block;
  width: 100%;
  height: 100%;
  line-height: 20px;
  border-radius: 2px;
  background-color: rgba(255, 255, 255, 1);
  color: rgba(136, 136, 136, 1);
  font-size: 14px;
  text-align: left;
  font-family: PingFangSC-regular;
  border: 1px solid rgba(187, 187, 187, 1);
}

:where(.css-dev-only-do-not-override-1cqaw7h).ant-col-2 {
  max-width: 11%;
  flex: 0 0 11%;
}

.seven {
  flex: 0 0 26%;
  max-width: 26%;
}
.ant-table-cell {
    font-weight: 400 !important;
  }

.editable-row-operations a {
  margin-right: 8px;
}
.ant-table-wrapper{
  margin-top: 66px;
}
.ant-table-thead .ant-table-cell{
  font-weight: 400;
}
a-table :deep(.ant-table-thead>tr>th) {
  font-weight: 400 !important;
}
</style>
