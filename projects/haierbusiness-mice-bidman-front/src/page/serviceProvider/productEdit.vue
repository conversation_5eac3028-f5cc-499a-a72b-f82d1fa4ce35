<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Input as hInput,
  InputNumber as hInputNumber,
  Form as hForm,
  FormItem as hFormItem,
  Modal as hModal,
  Upload as hUpload,
  message
} from 'ant-design-vue';
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue';
import { PriceTypeConstant } from '@haierbusiness-front/common-libs';
import { RoomTypeConstant } from '@haierbusiness-front/common-libs';
import { IPriceInquiryFilter, IPriceInquiry } from '@haierbusiness-front/common-libs';
import { fileApi } from '@haierbusiness-front/apis';
import { priceInquiryApi } from '@haierbusiness-front/apis';
import { useRoute, useRouter } from 'vue-router';
import { UploadOutlined, PlusOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';
import { h } from 'vue';
import { min, result } from 'lodash';
import path from 'path';


const route = useRoute();
const router = useRouter()
const loading = ref(false);

// 审批流程相关
const businessProcess = import.meta.env.VITE_BUSINESS_PROCESS_URL;
const approvalModalShow = ref(false);
const approveCode = ref<string>(''); // 审批流程Code
const venueTitle = ref('')

//是否青岛
const isLocal = ref<Boolean>(false);
const inquiryId = ref<number | null>(null);
const platformHotelCode = ref<number | null>(null);
const codeFromRoute = ref<string | null>(null);

// 添加控制显示的变量
const enableQuarter = ref(false);
const enablePlaceChange = ref(false);

// 表单数据
const formData = reactive({
  // 淡旺季设置模式
  seasonSettingMode: 1, // 1: 按照时季设置, 2: 无淡旺季

  // 淡季时间列表（改为数组，可添加删除）
  lightSeasons: [
    {
      id: 1,
      startDate: undefined,
      endDate: undefined
    }
  ],

  // 旺季时间列表（可添加、删除）
  peakSeasons: [
    {
      id: 1,
      startDate: undefined,
      endDate: undefined
    }
  ],

  // 价格阶梯模式
  priceTierSettingMode: 1, // 1: 按照阶梯设置价格, 2: 不按阶梯设置价格
  priceTiers: [
    { id: 1, tier: '1', min: 1, max: 50 },
    { id: 2, tier: '2', min: 51, max: null as number | null }
  ],

  // 住宿价格表
  rooms: [
    {
      id: 1,
      roomType: 1, // 大床房
      lightSeasonMarketPrice: 0,
      lightSeasonAgreementPriceUnder50: 0,
      lightSeasonAgreementPriceAbove50: 0,
      peakSeasonMarketPrice: 0,
      peakSeasonAgreementPriceUnder50: 0,
      peakSeasonAgreementPriceAbove50: 0,
      marketPrice: 0, // 新增：独立的门市价 (code=30)
      agreementPrice: 0, // 新增：独立的协议价 (code=20)
      Marketprice: 0,//市场价
      path: null,//文件
    },
    {
      id: 2,
      roomType: 2, // 双床房
      lightSeasonMarketPrice: 0,
      lightSeasonAgreementPriceUnder50: 0,
      lightSeasonAgreementPriceAbove50: 0,
      peakSeasonMarketPrice: 0,
      peakSeasonAgreementPriceUnder50: 0,
      peakSeasonAgreementPriceAbove50: 0,
      marketPrice: 0, // 新增：独立的门市价 (code=30)
      agreementPrice: 0, // 新增：独立的协议价 (code=20)
      Marketprice: 0,//市场价
      path: null,//文件
    },
    {
      id: 3,
      roomType: 3, // 套房
      lightSeasonMarketPrice: 0,
      lightSeasonAgreementPriceUnder50: 0,
      lightSeasonAgreementPriceAbove50: 0,
      peakSeasonMarketPrice: 0,
      peakSeasonAgreementPriceUnder50: 0,
      peakSeasonAgreementPriceAbove50: 0,
      marketPrice: 0, // 新增：独立的门市价 (code=30)
      agreementPrice: 0, // 新增：独立的协议价 (code=20)
      Marketprice: 0,//市场价
      path: null,//文件
    }
  ],
  // 会场价格表
  venues: [] as Array<{
    id: number,
    platformPlaceId: number,
    venueName: string;
    area: number;
    maxNum: number;
    height: number;
    length: number;
    width: number;
    floor: number;
    hasColumn: string;
    lightSeasonMarketPrice: number;
    lightSeasonAgreementPrice: number;
    peakSeasonMarketPrice: number;
    peakSeasonAgreementPrice: number;
    vendorSeasonMarketPrice: number;
    vendorSeasonAgreementPrice: number; // 新增：旺季全天协议价
    vendorSeasonHalfDayMarketPrice: number; // 新增：旺季半天市场价
    vendorSeasonHalfDayAgreementPrice: number; // 新增：旺季半天协议价
    Ordinarymarketprice: number;//门市价半天价
    Ordinaryagreementprice: number;//协议半天价
    marketPrice: number; // 新增：门市价 (code=30)
    agreementPrice: number; // 新增：协议价 (code=20)
    dayMarketPrice: number,//全天市场价
    HalfDayMarketPrice: number,//半天市场价
    path: string | null,//文件
    venueImages?: {
      id: number;
      mainId: number;
      path: string;
      tableName: string;
    }[];
    tableModes?: Array<{
      id: number | null;
      mode: string;
      supported: string;
      maxCapacity: number | null;
      image: string[] | string;
    }>;
  }>,

  // 新增会场相关数据
  venueFormVisible: false,
  // 记录当前正在编辑的会场索引
  editingVenueIndex: null as number | null,
  newVenue: {
    id: '',
    venueName: '',
    maxNum: 0,
    hasColumn: '否',
    area: 0,
    height: 0,
    length: 0,
    width: 0,
    floor: 1,
    lightSeasonMarketPrice: 0,
    lightSeasonAgreementPrice: 0,
    peakSeasonMarketPrice: 0,
    peakSeasonAgreementPrice: 0,
    vendorSeasonMarketPrice: 0,
    vendorSeasonAgreementPrice: 0, // 新增：旺季全天协议价
    vendorSeasonHalfDayMarketPrice: 0, // 新增：旺季半天市场价
    vendorSeasonHalfDayAgreementPrice: 0, // 新增：旺季半天协议价
    Ordinarymarketprice: 0,//门市价半天价
    Ordinaryagreementprice: 0,//协议半天价
    marketPrice: 0, // 新增：门市价 (code=30)
    agreementPrice: 0, // 新增：协议价 (code=20)
    venueImages: [] as any,
    tableModes: [
      { id: '', mode: 'U型式', supported: '是', maxCapacity: 120, image: {} },
      { id: '', mode: '宴会会式', supported: '是', maxCapacity: 100, image: {} },
      { id: '', mode: '剧院式', supported: '是', maxCapacity: 200, image: {} },
      { id: '', mode: '课桌式', supported: '否', maxCapacity: null, image: {} },
      { id: '', mode: '海岛式', supported: '否', maxCapacity: null, image: {} },
      { id: '', mode: '鸡尾酒', supported: '否', maxCapacity: null, image: {} },
      { id: '', mode: '鱼骨式', supported: '否', maxCapacity: null, image: {} }
    ]
  }
});

// 上传图片相关
const venueImageFileList = ref<Array<any>>([]);
const previewVisible = ref(false);
const previewImage = ref('');
const previewTitle = ref('预览');
const previewImages = ref<Array<{ src: string; title?: string }>>([]);

// 上传配置
const uploadLoading = ref(false);
const baseUrl = import.meta.env.VITE_BUSINESS_URL || '';

// 表格图片上传相关
const tableImageFileList = ref<Record<string, any[]>>({
  'U型式': [],
  '宴会式': [],
  '剧院式': [],
  '课桌式': [],
  '海岛式': [],
  '鸡尾酒': [],
  '鱼骨式': []
});

// 获取房型名称的函数
const getRoomTypeName = (roomType: number): string => {
  switch (roomType) {
    case RoomTypeConstant.BIG.code:
      return RoomTypeConstant.BIG.desc;
    case RoomTypeConstant.DOUBLE.code:
      return RoomTypeConstant.DOUBLE.desc;
    case RoomTypeConstant.ROOM.code:
      return RoomTypeConstant.ROOM.desc;
    default:
      return '';
  }
};

// 住宿表格列
const roomColumns = computed(() => {
  // 基础列 - 床型始终显示
  const baseColumns = [
    {
      title: '床型',
      dataIndex: 'roomType',
      key: 'roomType',
      width: 80,
      customRender: ({ text }: { text: number }) => {
        return getRoomTypeName(text);
      }
    }
  ];

  // 如果设置为按淡旺季设置(seasonSettingMode === 1)，显示完整的淡旺季价格列
  if (formData.seasonSettingMode === 1 && enableQuarter.value) {
    return [
      ...baseColumns,
      {
        title: '淡季门市价',
        dataIndex: 'lightSeasonMarketPrice',
        key: 'lightSeasonMarketPrice',
        width: 150
      },
      {
        title: '淡季协议价',
        dataIndex: 'lightSeasonAgreementPriceUnder50',
        key: 'lightSeasonAgreementPriceUnder50',
        width: 200
      },
      {
        title: '旺季门市价',
        dataIndex: 'peakSeasonMarketPrice',
        key: 'peakSeasonMarketPrice',
        width: 150
      },
      {
        title: '旺季协议价',
        dataIndex: 'peakSeasonAgreementPriceUnder50',
        key: 'peakSeasonAgreementPriceUnder50',
        width: 200
      },
    ];
  } else {
    // 无淡旺季时(seasonSettingMode === 2)，只显示门市价(code=30)和协议价(code=20)
    return [
      ...baseColumns,
      {
        title: '门市价',
        dataIndex: 'marketPrice',
        key: 'marketPrice',
        width: 120,
        customRender: ({ text }: { text: any }) => {
          return text ? `${text.toFixed(2)}元` : '暂无数据';
        }
      },
      {
        title: '协议价',
        dataIndex: 'agreementPrice',
        key: 'agreementPrice',
        width: 120,
        customRender: ({ text }: { text: any }) => {
          return text ? `${text.toFixed(2)}元` : '暂无数据';
        }
      },
    ];
  }
});

// 图片表格列定义
const customRender = ({ record }: { record: any }) => {
  if (record.venueImages && record.venueImages.length > 0) {
    // 在表格中只显示第一张图片
    return h('div', {
      style: {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center'
      }
    }, [
      // 显示第一张图片
      h('img', {
        src: record.venueImages[0].path,
        style: {
          width: '80px',
          height: '60px',
          objectFit: 'cover',
          border: '1px solid #eee',
          borderRadius: '2px',
          cursor: 'pointer'
        },
        onClick: () => {
          // 点击放大查看所有图片
          previewImage.value = record.venueImages[0].path;
          previewImages.value = record.venueImages.map((url: any) => ({
            src: url.path,
            title: record.venueName || ''
          }));
          previewVisible.value = true;
        }
      }),
      // 如果有多张图片，显示 +n张 的文本
      record.venueImages.length > 1 ? h('div', {
        style: {
          fontSize: '12px',
          color: '#1890ff',
          cursor: 'pointer',
          marginTop: '4px'
        },
        onClick: () => {
          // 点击查看全部图片
          previewImage.value = record.venueImages[0].path;
          previewImages.value = record.venueImages.map((url: any) => ({
            src: url.path,
            title: record.venueName || ''
          }));
          previewVisible.value = true;
        }
      }, `+${record.venueImages.length - 1}张`) : null
    ]);
  }
  return '-';
};

// 会场表格列
const venueColumns = computed(() => {
  // 基础列 - 始终显示
  const baseColumns = [
    {
      title: '会场名称',
      dataIndex: 'venueName',
      key: 'venueName',
      width: 120
    },
    {
      title: '会场图片',
      dataIndex: 'image',
      key: 'image',
      width: 100,
      customRender
    },
    {
      title: '面积 (㎡)',
      dataIndex: 'area',
      key: 'area',
      width: 90,
      customRender: ({ text }: { text: any }) => {
          return text ? `${text.toFixed(2)}` : '暂无数据';
        }
    },
    {
      title: '长 (m)',
      dataIndex: 'length',
      key: 'length',
      width: 90,
      customRender: ({ text }: { text: any }) => {
          return text ? `${text.toFixed(2)}` : '暂无数据';
        }
    },
    {
      title: '宽 (m)',
      dataIndex: 'width',
      key: 'width',
      width: 90,
      customRender: ({ text }: { text: any }) => {
          return text ? `${text.toFixed(2)}` : '暂无数据';
        }
    },
    {
      title: '楼层',
      dataIndex: 'floor',
      key: 'floor',
      width: 90,
      customRender: ({ text }: { text: any }) => {
          return text ? `${text.toFixed(0)}` : '暂无数据';
        }
    },
    {
      title: '层高(m)',
      dataIndex: 'height',
      key: 'floor',
      width: 90,
      customRender: ({ text }: { text: any }) => {
          return text ? `${text.toFixed(2)}` : '暂无数据';
        }
    },
    {
      title: '是否有柱',
      dataIndex: 'hasColumn',
      key: 'hasColumn',
      width: 100
    },

  ];
  //操作列
  const actionColumns = [
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right',
      align: 'center',
      customRender: ({ record, index }: { record: any, index: number }) => {
        return h('div', {
          style: {
            display: 'flex',
            justifyContent: 'center',
            width: '100%',
            textAlign: 'center'
          }
        }, [
          h('a', {
            style: {
              color: '#1890ff',
              marginRight: '10px',
              textAlign: 'center'
            },
            onClick: () => editVenue(record, index)
          }, '编辑'),
          h('a', {
            style: {
              color: '#ff4d4f'
            },
            onClick: () => removeVenue(record, index)
          }, '删除')
        ]);
      }
    }
  ]

  // 如果季节设置模式为按淡旺季设置(seasonSettingMode === 1)，显示完整的淡旺季价格列
  if (formData.seasonSettingMode === 1 && isLocal.value) {
    const columnsWithPrices = [
      ...baseColumns,
      {
        title: '淡季全天门市价',
        dataIndex: 'lightSeasonMarketPrice',
        key: 'lightSeasonMarketPrice',
        width: 170
      },
      {
        title: '淡季全天协议价',
        dataIndex: 'lightSeasonAgreementPrice',
        key: 'lightSeasonAgreementPrice',
        width: 170
      },
      {
        title: '淡季半天门市价',
        dataIndex: 'peakSeasonMarketPrice',
        key: 'peakSeasonMarketPrice',
        width: 170
      },
      {
        title: '淡季半天协议价',
        dataIndex: 'peakSeasonAgreementPrice',
        key: 'peakSeasonAgreementPrice',
        width: 170
      },
      {
        title: '旺季全天门市价',
        dataIndex: 'vendorSeasonMarketPrice',
        key: 'vendorSeasonMarketPrice',
        width: 170
      },
      {
        title: '旺季全天协议价',
        dataIndex: 'vendorSeasonAgreementPrice',
        key: 'vendorSeasonAgreementPrice',
        width: 170
      },
      {
        title: '旺季半天门市价',
        dataIndex: 'vendorSeasonHalfDayMarketPrice',
        key: 'vendorSeasonHalfDayMarketPrice',
        width: 170
      },
      {
        title: '旺季半天协议价',
        dataIndex: 'vendorSeasonHalfDayAgreementPrice',
        key: 'vendorSeasonHalfDayAgreementPrice',
        width: 170
      },
      ...actionColumns
    ];
    return columnsWithPrices;
  } else if (formData.seasonSettingMode === 2 && isLocal.value) {
    // 如果seasonSettingMode === 2 (无淡旺季)，显示门市价和协议价列
    const columnsWithPrices = [
      ...baseColumns,
      {
        title: '门市价全天价',
        dataIndex: 'marketPrice', // 使用独立的marketPrice字段
        key: 'marketPrice',
        width: 150,
        customRender: ({ text }: { text: any }) => {
          return text ? `${text.toFixed(2)}元` : '暂无数据';
        }
      },
      {
        title: '门市价半天价',
        dataIndex: 'Ordinarymarketprice', // 使用淡季半天门市价字段作为半天价
        key: 'Ordinarymarketprice',
        width: 150,
        customRender: ({ text }: { text: any }) => {
          return text ? `${text.toFixed(2)}元` : '暂无数据';
        }
      },
      {
        title: '协议价全天价',
        dataIndex: 'agreementPrice', // 使用独立的agreementPrice字段
        key: 'agreementPrice',
        width: 150,
        customRender: ({ text }: { text: any }) => {
          return text ? `${text.toFixed(2)}元` : '暂无数据';
        }
      },
      {
        title: '协议半天价',
        dataIndex: 'Ordinaryagreementprice', // 使用淡季半天协议价字段
        key: 'Ordinaryagreementprice',
        width: 150,
        customRender: ({ text }: { text: any }) => {
          return text ? `${text.toFixed(2)}元` : '暂无数据';
        }
      },
      ...actionColumns
    ];
    return columnsWithPrices;
  } else if (!isLocal.value) {
    const columnsWithPrices = [
      ...baseColumns,
      ...actionColumns
    ]

    return columnsWithPrices
  }
});

// 弹窗中价格配置表格列定义
const priceConfigColumns = computed(() => {
  const baseColumns = [{
    title: '价格配置',
    dataIndex: 'priceType',
    width: 120
  }];
  if (formData.seasonSettingMode == 1) {
    return [
      ...baseColumns,
      {
        title: '淡季',
        children: [
          {
            title: '门市价(元)',
            dataIndex: 'marketPrice_light',
            key: 'marketPrice_light',
            width: 120
          },
          {
            title: '协议价(元)',
            dataIndex: 'agreementPrice_light',
            key: 'agreementPrice_light',
            width: 120
          }
        ]
      },
      {
        title: '旺季',
        children: [
          {
            title: '门市价(元)',
            dataIndex: 'marketPrice_peak',
            key: 'marketPrice_peak',
            width: 120
          },
          {
            title: '协议价(元)',
            dataIndex: 'agreementPrice_peak',
            key: 'agreementPrice_peak',
            width: 120
          }
        ]
      }
    ]
  } else {
    return [
      ...baseColumns,
      {
        title: '门市价(元)',
        dataIndex: 'marketPrice',
        key: 'marketPrice',
        width: 120
      },
      {
        title: '协议价(元)',
        dataIndex: 'agreementPrice',
        key: 'agreementPrice',
        width: 120
      }
    ]
  }
})

// 弹窗中价格配置表格数据
const priceConfigData = reactive([
  {
    key: '1',
    priceType: '半天',
    marketPrice_light: 0,
    agreementPrice_light: 0,
    marketPrice_peak: 0,
    agreementPrice_peak: 0,
    marketPrice: 0,//普通
    agreementPrice: 0,//普通
  },
  {
    key: '2',
    priceType: '全天',
    marketPrice_light: 0,
    agreementPrice_light: 0,
    marketPrice_peak: 0,
    agreementPrice_peak: 0,
    marketPrice: 0,//普通
    agreementPrice: 0,//普通
  }
]);

// 处理文件上传请求
const uploadRequest = (options: any) => {
  uploadLoading.value = true;

  const uploadFormData = new FormData();
  uploadFormData.append('file', options.file);

  fileApi.upload(uploadFormData)
    .then((response) => {
      const filePath = baseUrl + response.path;
      const fileObj = {
        uid: options.file.uid,
        name: options.file.name,
        status: 'done',
        url: filePath,
        filePath: filePath,
        fileName: options.file.name
      };

      // 添加到文件列表
      venueImageFileList.value.push(fileObj);
      console.log(venueImageFileList.value, "venueImageFileList.value");


      if (removeImage) {
        formData.newVenue.venueImages.push({
          id: removeImage.id || null,
          path: filePath || null,
          mainId: removeImage.mainId || null,
          tableName: removeImage.tableName || null,
          type: removeImage.type || null,
        });
      } else {
        formData.newVenue.venueImages.push({
          path: filePath || null,
          tableName: hotelDetails.value.resourceHotelPlaces[0].images[0].tableName,
          type: 1 || null,
        });
      }

      console.log(formData.newVenue.venueImages, "formData.newVenue.venueImages");

      options.onProgress(100);
      options.onSuccess(response, options.file);
    })
    .catch((error) => {
      console.error('文件上传失败:', error);
      console.error('错误详情:', JSON.stringify(error));
      message.error('文件上传失败，请检查网络连接或文件格式');
      options.onError(error);
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};

//记录当前删除的下标
let removeIndex = ref(0)
//记录当前删除的图片信息
let removeImage: { id: number; mainId: number; tableName: string; type: number; } | null = null
// 移除图片
const handleImageRemove = (file: any) => {
  const index = venueImageFileList.value.findIndex(item => item.uid === file.uid);
  removeIndex.value = index
  if (index !== -1) {
    removeImage = formData.newVenue.venueImages[removeIndex.value]
    console.log(removeImage, "当前删除：removeImage", removeIndex.value);
    venueImageFileList.value.splice(index, 1);
    formData.newVenue.venueImages.splice(index, 1);
  }
  return true;
};

// 预览图片
const handlePreview = async (file: any) => {
  if (file.url) {
    previewImage.value = file.url;
    previewVisible.value = true;

    // 如果传入的是会场记录，处理多张图片的预览
    if (file.venueImages && Array.isArray(file.venueImages)) {
      previewImages.value = file.venueImages.map((url: string) => ({
        src: url,
        title: file.venueName || url.substring(url.lastIndexOf('/') + 1)
      }));
    } else {
      // 单张图片预览
      previewImages.value = [{
        src: file.url,
      }];
    }
  }
};


const beforeUpload = (file: Blob) => {
  return new Promise((resolve, reject) => {
    // 第一阶段：同步校验文件类型和大小
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      message.error('只能上传图片文件!');
      return reject(false);
    }

    const isLt5M = file.size / 1024 / 1024 < 5;
    if (!isLt5M) {
      message.error('图片大小不能超过5MB!');
      return reject(false);
    }

    // 第二阶段：异步检测图片尺寸
    const reader = new FileReader();
    reader.onload = (e) => {
      const img = new Image();
      img.onload = () => {
        message.success(`图片尺寸: ${img.naturalWidth} x ${img.naturalHeight}`);
        resolve(true); // 所有验证通过
      };
      img.onerror = () => reject('图片加载失败');
      img.src = e.target.result;
    };
    reader.readAsDataURL(file);
  });
};

// 上传桌型图片
const uploadTableImage = (options: any, mode: string) => {
  uploadLoading.value = true;

  const uploadFormData = new FormData();
  uploadFormData.append('file', options.file);

  fileApi.upload(uploadFormData)
    .then((response: any) => {
      const filePath = baseUrl + response.path;
      const fileObj = {
        uid: options.file.uid,
        name: options.file.name,
        status: 'done',
        url: filePath,
        filePath: filePath,
        fileName: options.file.name
      };

      // 添加到文件列表
      if (!tableImageFileList.value[mode]) {
        tableImageFileList.value[mode] = [];
      }

      // 更新为只保留最新上传的图片
      tableImageFileList.value[mode] = [fileObj];
      console.log(tableModeList, "tableModeList");
      // 更新桌型图片
      updateTableImage(mode, filePath)

      options.onProgress(100);
      options.onSuccess(response, options.file);
    })
    .catch((error: any) => {
      console.error('文件上传失败:', error);
      message.error('文件上传失败');
      options.onError(error);
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};



const updateTableImage = (mode: string, filePath: string) => {
  if (!formData?.newVenue?.tableModes) return;

  const targetIndex = formData.newVenue.tableModes.findIndex(item => item.mode === mode);
  if (targetIndex === -1) return;

  const updatedTableModes = [...formData.newVenue.tableModes];
  const sourceImage = tableModeList?.find(item =>
    item.imageData?.mode === mode
  )?.imageData?.image;

  updatedTableModes[targetIndex] = {
    ...updatedTableModes[targetIndex],
    image: sourceImage
      ? {
        ...sourceImage,
        path: filePath,
        // 保留原有id和mainId（如果存在）
        ...(sourceImage.id && { id: sourceImage.id }),
        ...(sourceImage.mainId && { mainId: sourceImage.mainId })
      }
      : {
        path: filePath,
        tableName: "resource_hotel_place_layout",
        type: 1
        // 不包含id和mainId
      }
  };

  formData.newVenue.tableModes = updatedTableModes;
};




//记录删除桌型图片的信息
let tableModeList: any = []

// 移除桌型图片
const handleTableImageRemove = (file: any, mode: string) => {
  if (tableImageFileList.value[mode]) {
    tableImageFileList.value[mode] = [];

    // 清除桌型图片
    const tableModeIndex = formData.newVenue.tableModes.findIndex(item => item.mode === mode);
    console.log(formData.newVenue.tableModes[tableModeIndex], tableModeIndex);

    if (tableModeIndex !== -1) {
      const currentImage = formData.newVenue.tableModes[tableModeIndex];
      tableModeList.push({
        imageData: currentImage || ''
      });
      formData.newVenue.tableModes[tableModeIndex].image = '';
    }
  }
  return true;
};

// 修改桌型支持状态
const handleTableModeChange = (value: string, index: number) => {
  formData.newVenue.tableModes[index].supported = value;

  // 如果设置为"否"，清空最大容纳人数
  if (value === '否') {
    formData.newVenue.tableModes[index].maxCapacity = null;
  } else if (formData.newVenue.tableModes[index].maxCapacity === null) {
    // 如果设置为"是"且最大容纳人数为null，给个默认值
    formData.newVenue.tableModes[index].maxCapacity = 100;
  }
};

// 显示新增会场弹框
const showAddVenueModal = () => {
  venueTitle.value = '新增会场'
  formData.editingVenueIndex = null
  console.log(formData.newVenue, "formData.newVenue");

  // 重置新增会场表单
  formData.newVenue = {
    venueName: '',
    hasColumn: '否',
    area: 0,
    height: 0,
    length: 0,
    width: 0,
    floor: 1,
    lightSeasonMarketPrice: 0,
    lightSeasonAgreementPrice: 0,
    peakSeasonMarketPrice: 0,
    peakSeasonAgreementPrice: 0,
    vendorSeasonMarketPrice: 0,
    vendorSeasonAgreementPrice: 0, // 新增：旺季全天协议价
    vendorSeasonHalfDayMarketPrice: 0, // 新增：旺季半天市场价
    vendorSeasonHalfDayAgreementPrice: 0, // 新增：旺季半天协议价
    Ordinarymarketprice: 0,//门市价半天价(元)
    Ordinaryagreementprice: 0,//协议半天价(元)
    marketPrice: 0, // 新增：门市价 (code=30)
    agreementPrice: 0, // 新增：协议价 (code=20)
    venueImages: [],
    tableModes: [
      { mode: 'U型式', supported: '是', maxCapacity: 120, image: '' },
      { mode: '宴会式', supported: '是', maxCapacity: 100, image: '' },
      { mode: '剧院式', supported: '是', maxCapacity: 200, image: '' },
      { mode: '课桌式', supported: '否', maxCapacity: null, image: '' },
      { mode: '海岛式', supported: '否', maxCapacity: null, image: '' },
      { mode: '鸡尾酒', supported: '否', maxCapacity: null, image: '' },
      { mode: '鱼骨式', supported: '否', maxCapacity: null, image: '' }
    ]
  };

  // 重置价格配置数据
  priceConfigData.forEach(item => {
    item.marketPrice_light = 0;
    item.agreementPrice_light = 0;
    item.marketPrice_peak = 0;
    item.agreementPrice_peak = 0;
    item.marketPrice = 0;
    item.agreementPrice = 0;
  });

  // 重置图片上传列表
  venueImageFileList.value = [];
  tableImageFileList.value = {
    'U型式': [],
    '宴会式': [],
    '剧院式': [],
    '课桌式': [],
    '海岛式': [],
    '鸡尾酒': [],
    '鱼骨式': []
  }

  // 显示弹框
  formData.venueFormVisible = true;
};

// 取消新增会场
const cancelAddVenue = () => {
  formData.venueFormVisible = false;
};

// 确认新增会场
const confirmAddVenue = () => {

  // 表单验证
  if (!formData.newVenue.venueName) {
    message.error('请输入会场名称');
    return;
  }
  if (!formData.newVenue.hasColumn) {
    message.error('请选择是否有柱');
    return;
  }
  if (formData.newVenue.area <= 0) {
    message.error('请输入面积');
    return;
  }
  if (formData.newVenue.height <= 0) {
    message.error('请输入层高');
    return;
  }
  if (formData.newVenue.length <= 0) {
    message.error('请输入长度');
    return;
  }
  if (formData.newVenue.width <= 0) {
    message.error('请输入宽度');
    return;
  }
  if (formData.newVenue.floor < 1) {
    message.error('请输入楼层');
    return;
  }

  if (formData.seasonSettingMode == 1 && isLocal.value) {
    //半天淡旺季
    if (priceConfigData[0].marketPrice_light <= 0) {
      message.error('请输入淡季半天门市价')
      return
    }
    if (priceConfigData[0].agreementPrice_light <= 0) {
      message.error('请输入淡季半天协议价')
      return
    }
    if (priceConfigData[0].marketPrice_peak <= 0) {
      message.error('请输入旺季半天门市价')
      return
    }
    if (priceConfigData[0].agreementPrice_peak <= 0) {
      message.error('请输入旺季半天协议价')
      return
    }
    //全天淡旺季
    if (priceConfigData[1].marketPrice_light <= 0) {
      message.error('请输入淡季全天门市价')
      return
    }
    if (priceConfigData[1].agreementPrice_light <= 0) {
      message.error('请输入淡季全天协议价')
      return
    }
    if (priceConfigData[1].marketPrice_peak <= 0) {
      message.error('请输入旺季全天门市价')
      return
    }
    if (priceConfigData[1].agreementPrice_peak <= 0) {
      message.error('请输入旺季全天协议价')
      return
    }
  } else if (formData.seasonSettingMode == 2 && isLocal.value) {
    //半天
    if (priceConfigData[0].marketPrice <= 0) {
      message.error('请输入半天门市价')
      return
    }
    if (priceConfigData[0].agreementPrice <= 0) {
      message.error('请输入半天门市价')
      return
    }
    //全天
    if (priceConfigData[1].marketPrice <= 0) {
      message.error('请输入全天协议价')
      return
    }
    if (priceConfigData[1].agreementPrice <= 0) {
      message.error('请输入全天协议')
      return
    }
  }

  if (formData.newVenue.venueImages.length < 2) {
    message.error('会场图片最少两张');
    return;
  }

  const tableImg = formData.newVenue.tableModes.some((item) => {
    if (item.supported === '是' && !item.image) {
      message.error(`${item.mode}桌型如果支持请上传图片`);
      return true;
    }
    return false;
  });

  if (tableImg) {
    return
  }




  try {
    // 从价格配置表获取价格数据
    const halfDayPrices = priceConfigData[0];
    const fullDayPrices = priceConfigData[1];

    // 创建新会场对象
    const newVenueObj = {
      id: formData.newVenue.id,
      venueName: formData.newVenue.venueName,
      hasColumn: formData.newVenue.hasColumn,
      area: formData.newVenue.area,
      height: formData.newVenue.height,
      length: formData.newVenue.length,
      width: formData.newVenue.width,
      floor: formData.newVenue.floor,
      lightSeasonMarketPrice: Number(fullDayPrices.marketPrice_light) || 0,
      lightSeasonAgreementPrice: Number(fullDayPrices.agreementPrice_light) || 0,
      peakSeasonMarketPrice: Number(halfDayPrices.marketPrice_light) || 0,
      peakSeasonAgreementPrice: Number(halfDayPrices.agreementPrice_light) || 0,
      vendorSeasonMarketPrice: Number(fullDayPrices.marketPrice_peak) || 0,
      vendorSeasonAgreementPrice: Number(fullDayPrices.agreementPrice_peak) || 0, // 新增：旺季全天协议价
      vendorSeasonHalfDayMarketPrice: Number(halfDayPrices.marketPrice_peak) || 0, // 新增：旺季半天市场价
      vendorSeasonHalfDayAgreementPrice: Number(halfDayPrices.agreementPrice_peak) || 0, // 新增：旺季半天协议价
      Ordinarymarketprice: Number(halfDayPrices.marketPrice) || 0,//门市价半天价(元)
      Ordinaryagreementprice: Number(halfDayPrices.agreementPrice) || 0,//协议半天价(元)
      marketPrice: Number(fullDayPrices.marketPrice) || 0, //全天门市价
      agreementPrice: Number(fullDayPrices.agreementPrice) || 0, //全天协议价价
      venueImages: formData.newVenue.venueImages,
      tableModes: formData.newVenue.tableModes
    };

    // 判断是编辑还是新增
    if (formData.editingVenueIndex !== undefined && formData.editingVenueIndex !== null) {
      // 编辑现有会场
      formData.venues[formData.editingVenueIndex] = newVenueObj;
      console.log('更新会场数据:', newVenueObj);
      message.success('更新会场成功');
      // 重置编辑索引
      formData.editingVenueIndex = null;
    } else {
      // 添加到会场数组
      formData.venues.push(newVenueObj);
      console.log('新增会场数据:', newVenueObj);
      message.success('新增会场成功');
    }

    // 使用路由上的ID作为唯一key
    const resourceHotelInquiryId = route.query.id ? String(route.query.id) : `venue_${Date.now()}`;

    // 创建包含所有会场的数据对象
    const allVenuesData = {
      venues: formData.venues
    };

    // 保存所有会场数据到sessionStorage (改为临时会话存储)
    sessionStorage.setItem(`venues_${resourceHotelInquiryId}`, JSON.stringify(allVenuesData));

    // 打印当前所有会场数据
    console.log('所有会场数据:', allVenuesData);

    // 关闭弹框
    formData.venueFormVisible = false;
  } catch (error) {
    console.error('操作会场出错:', error);
    message.error('操作失败');
  }
};

// 修改添加会场函数
const addVenue = () => {
  showAddVenueModal();
};


// 房间类型配置
const roomTypes = [
  { name: '大床房', index: 0 },
  { name: '双床房', index: 1 },
  { name: '套房', index: 2 }
]

// 季节模式验证规则
const validationRules = {
  1: [ // 季节模式1
    { field: 'lightSeasonMarketPrice', msg: '淡季门市价' },
    { field: 'lightSeasonAgreementPriceUnder50', msg: '淡季协议价' },
    { field: 'peakSeasonMarketPrice', msg: '旺季门市价' },
    { field: 'peakSeasonAgreementPriceUnder50', msg: '旺季协议价' }
  ],
  2: [ // 季节模式2
    { field: 'marketPrice', msg: '门市价' },
    { field: 'agreementPrice', msg: '协议价' }
  ]
}

const validateForm = (formData, isLocal, message) => {
  if (!isLocal.value) return true

  const rules = validationRules[formData.seasonSettingMode]
  if (!rules) return true

  for (const roomType of roomTypes) {
    const room = formData.rooms[roomType.index]
    if (!room) continue

    for (const rule of rules) {
      if (room[rule.field] <= 0) {
        message.error(`请填写${roomType.name}${rule.msg}`)
        return false
      }
    }
  }
  return true
}
//从原数据找市场价
const priceResultsEnd = (id,num)=>{
  const venueItem = hotelDetails.value.resourceHotelPlaces.find(item=>item.id == id)
  const result = venueItem.priceResults.find(item=>item.priceItem == num)
  if(result){
    return result
  }else{
    return null
  }
}
const venueAdd = (id)=>{
  const result = hotelDetails.value.resourceHotelPlaces.find(item=>item.id == id)
  if(result){
    return true
  }else{
    return false
  }
}

// 提交表单
const submitForm = () => {
  console.log(isLocal, "isLocal");

  if (!validateForm(formData, isLocal, message)) {
    return
  }


  if (formData.venues.length == 0) {
    message.error('请添加会场')
    return
  }
  const resourceHotelInquiryId = route.query.id ? Number(route.query.id) : null;
  console.log(resourceHotelInquiryId, "resourceHotelInquiryId");


  // 准备淡旺季数据
  interface QuarterItem {
    startDate: string;
    endDate: string;
    season: number;
  }
  let resourceHotelQuarters: QuarterItem[] = [];

  if (formData.seasonSettingMode == 1) {
    // 添加所有淡季数据
    formData.lightSeasons.forEach(season => {
      if (season.startDate && season.endDate) {
        resourceHotelQuarters.push({
          startDate: dayjs(season.startDate).format('YYYY-MM-DD'),
          endDate: dayjs(season.endDate).format('YYYY-MM-DD'),
          season: 1 // 淡季code为1
        });
      }
    });

    // 添加所有旺季数据
    formData.peakSeasons.forEach(season => {
      if (season.startDate && season.endDate) {
        resourceHotelQuarters.push({
          startDate: dayjs(season.startDate).format('YYYY-MM-DD'),
          endDate: dayjs(season.endDate).format('YYYY-MM-DD'),
          season: 2 // 旺季code为2
        });
      }
    });
  } else if (formData.seasonSettingMode == 2) {
    resourceHotelQuarters = [];
  }


  console.log(formData.venues, "formData.venues9999");

  // 格式化会场数据
  const formattedVenues = formData.venues.map((venue, index) => {
    const validModes = venue.tableModes?.filter(mode =>
      mode.supported === '是' && mode.maxCapacity != null
    ) || [];
    const maxNum = validModes.length
      ? Math.max(...validModes.map(mode => +mode.maxCapacity || 0))
      : venue.maxNum | 100;

    let placesImg: { type: number; path: string; }[] = []
    venue.venueImages.map((item) => {
      placesImg.push({
        type: 2,
        path: item.path
      })
    })

    // 格式化会场布局信息
    const tableResults = venue.tableModes
      ? venue.tableModes
        .filter(mode => mode.supported === '是')
        .map(mode => {
          // 桌型映射
          let tableTypeCode = 1; // 默认为U型式
          switch (mode.mode) {
            case 'U型式': tableTypeCode = 1; break;
            case '宴会式': tableTypeCode = 2; break; // 董事会式
            case '剧院式': tableTypeCode = 3; break;
            case '海岛式': tableTypeCode = 4; break;
            case '鸡尾酒': tableTypeCode = 5; break; // 酒会式
            case '课桌式': tableTypeCode = 6; break;
            case '鱼骨式': tableTypeCode = 7; break;
            default: tableTypeCode = 1;
          }

          // 准备图片URL
          const imageUrl: any = mode.image || null; // 确保imageUrl一定有值

          // 构建tableUrls数组，模拟服务器返回的格式
          let tableUrls: string[] | string = [];

          // 添加图片URL到tableUrls
          if (imageUrl) {
            tableUrls.push(imageUrl)
          }

          return {
            tableType: tableTypeCode,
            maxNum: mode.maxCapacity || 100,
            type: 2,
            imageUrl: imageUrl.path // 所有图片URL数组
          };
        })
      : [];
    let priceResults: any = []
    let isEqual = true
    if (venueAdd(venue.id)) {
      isEqual = true
      console.log('编辑会场');
      if (formData.seasonSettingMode == 1 && isLocal.value) {
        const priceItemList = [
          {
            itemType: 2,
            priceItem: 110, // 淡季全天门市价
            price: venue.lightSeasonMarketPrice,
          },
          {
            itemType: 2,
            priceItem: 130, // 淡季全天协议价
            price: venue.lightSeasonAgreementPrice,
          },
          {
            itemType: 2,
            priceItem: 100, // 淡季半天门市价
            price: venue.peakSeasonMarketPrice,
          },
          {
            itemType: 2,
            priceItem: 120, // 淡季半天协议价
            price: venue.peakSeasonAgreementPrice,
          },
          {
            itemType: 2,
            priceItem: 150, // 旺季全天门市价
            price: venue.vendorSeasonMarketPrice,
          },
          {
            itemType: 2,
            priceItem: 170, // 旺季全天协议价
            price: venue.vendorSeasonAgreementPrice,
          },
          {
            itemType: 2,
            priceItem: 140, // 旺季半天门市价
            price: venue.vendorSeasonHalfDayMarketPrice,
          },
          {
            itemType: 2,
            priceItem: 160, // 旺季半天协议价
            price: venue.vendorSeasonHalfDayAgreementPrice,
          },
          {
            itemType: 2,
            priceItem: 180, // 全天市场价
            price: Number(priceResultsEnd(venue.id,180).price.toFixed(2)),
            type: 25,
            path: priceResultsEnd(venue.id,180).path
          },
          {
            itemType: 2,
            priceItem: 190, // 半天市场价
            price: Number(priceResultsEnd(venue.id,190).price.toFixed(2)),
            type: 25,
            path: priceResultsEnd(venue.id,190).path
          }
        ]
        priceResults = priceItemList
      } else if (formData.seasonSettingMode == 2 && isLocal.value) {
        const priceItemList = [
          {
            itemType: 2,
            priceItem: 200, // 半天门市价
            price: venue.Ordinarymarketprice,
          },
          {
            itemType: 2,
            priceItem: 220, // 半天协议价
            price: venue.Ordinaryagreementprice,
          },
          {
            itemType: 2,
            priceItem: 210, // 全天门市价 (LIST_PRICE)
            price: venue.marketPrice, // 使用独立的marketPrice字段
          },
          {
            itemType: 2,
            priceItem: 230, // 全天协议价 (AGREEMENT_PRICE)
            price: venue.agreementPrice, // 使用独立的agreementPrice字段
          },
          {
            itemType: 2,
            priceItem: 180, // 全天市场价
            price: Number(priceResultsEnd(venue.id,180).price.toFixed(2)),
            type: 25,
            path: priceResultsEnd(venue.id,180).path
          },
          {
            itemType: 2,
            priceItem: 190, // 半天市场价
            price: Number(priceResultsEnd(venue.id,190).price.toFixed(2)),
            type: 25,
            path: priceResultsEnd(venue.id,190).path
          }
        ]
        priceResults = priceItemList
      } else if (!isLocal.value) {
        priceResults = []
      }
    } else {
      console.log('新增会场');
      isEqual = false
      if (formData.seasonSettingMode == 1 && isLocal.value) {
        const priceItemList = [
          {
            priceItem: 110, // 淡季全天门市价
            price: venue.lightSeasonMarketPrice,
          },
          {
            priceItem: 130, // 淡季全天协议价
            price: venue.lightSeasonAgreementPrice,
          },
          {
            priceItem: 100, // 淡季半天门市价
            price: venue.peakSeasonMarketPrice,
          },
          {
            priceItem: 120, // 淡季半天协议价
            price: venue.peakSeasonAgreementPrice,
          },
          {
            priceItem: 150, // 旺季全天门市价
            price: venue.vendorSeasonMarketPrice,
          },
          {
            priceItem: 170, // 旺季全天协议价
            price: venue.vendorSeasonAgreementPrice,
          },
          {
            priceItem: 140, // 旺季半天门市价
            price: venue.vendorSeasonHalfDayMarketPrice,
          },
          {
            priceItem: 160, // 旺季半天协议价
            price: venue.vendorSeasonHalfDayAgreementPrice,
          },
        ]
        priceItemList.forEach((item) => {
          priceResults.push({
            itemType: 2,
            priceItem: item.priceItem,
            price: item.price,
          })
        })
      } else if (formData.seasonSettingMode == 2 && isLocal.value) {
        const priceItemList = [
          {
            priceItem: 200, // 半天门市价
            price: venue.Ordinarymarketprice,
          },
          {
            priceItem: 220, // 半天协议价
            price: venue.Ordinaryagreementprice,
          },
          {
            priceItem: 210, // 全天门市价 (LIST_PRICE)
            price: venue.marketPrice, // 使用独立的marketPrice字段
          },
          {
            priceItem: 230, // 全天协议价 (AGREEMENT_PRICE)
            price: venue.agreementPrice, // 使用独立的agreementPrice字段
          },
        ]
        priceItemList.forEach((item) => {
          priceResults.push({
            itemType: 2,
            priceItem: item.priceItem,
            price: item.price,
          })
        })
      } else if (!isLocal.value) {
        priceResults = []
      }
    }

    return {
      platformHotelCode: platformHotelCode.value,
      placeName: venue.venueName,
      platformPlaceId: index + 1 | venue.platformPlaceId,
      maxNum,
      area: venue.area,
      height: venue.height,
      length: venue.length,
      wide: venue.width, // 注意字段名变更：width -> wide
      floor: venue.floor,
      isPillar: venue.hasColumn === '是', // 转换为布尔值
      images: placesImg,
      priceResults,
      tableResults
    };
  });
  const hotelPrices: any = []
  let hotels: any = []
  formData.rooms.map((room, index) => {
    // 根据季节设置模式来决定提交哪些价格数据
    if (formData.seasonSettingMode == 1 && resourceHotelQuarters.length > 0) {
      const priceItemList = [
        {
          itemType: room.roomType,
          priceItem: 40,
          price: room.lightSeasonMarketPrice,
        },
        {
          itemType: room.roomType,
          priceItem: 50,
          price: room.lightSeasonAgreementPriceUnder50,
        },
        {
          itemType: room.roomType,
          priceItem: 70,
          price: room.peakSeasonMarketPrice,
        },
        {
          itemType: room.roomType,
          priceItem: 80,
          price: room.peakSeasonAgreementPriceUnder50,
        },
        {
          itemType: room.roomType,
          priceItem: 10,
          price: room?.Marketprice || 0,
          path: room.path,
          type: 25
        },
      ]
      hotels.push({
        roomType: room.roomType,
        prices: priceItemList
      })
    } else if (isLocal.value) {
      const priceItemList = [
        {
          itemType: room.roomType,
          priceItem: 30,
          price: room.marketPrice,
        },
        {
          itemType: room.roomType,
          priceItem: 20,
          price: room.agreementPrice,
        },
        {
          itemType: room.roomType,
          priceItem: 10,
          price: room?.Marketprice || 0,
          path: room.path,
          type: 25
        },
      ]
      hotels.push({
        roomType: room.roomType,
        prices: priceItemList
      })

    }
  })

  // 构建完整的提交数据对象
  const submitData = {
    id: hotelDetails.value.id,
    platformHotelCode: hotelDetails.value.platformHotelCode,
    platformHotelName: hotelDetails.value.platformHotelName,
    platformHotelAddress: hotelDetails.value.platformHotelAddress,
    platformHotelStar: hotelDetails.value.platformHotelStar,
    platformHotelArea: hotelDetails.value.platformHotelArea,
    isBid: hotelDetails.value.isBid,
    isLocal: hotelDetails.value.isLocal,
    reason: hotelDetails.value.reason,
    enableQuarter: hotelDetails.value.enableQuarter,
    enableStairs: hotelDetails.value.enableStairs,
    //住宿价格
    hotels,
    // 会场价格 - 使用新格式
    places: formattedVenues,
    quarters: resourceHotelQuarters
  };

  loading.value = true;
  console.log(submitData, "提交submitData");



  //调用API提交数据
  // priceInquiryApi.productEdit(submitData)
  //   .then(response => {
  //     hModal.success({
  //       width: '330px',
  //       content: '提交成功',
  //       okText: '确定',
  //       onOk: () => {
  //         if (response) {
  //           approveCode.value = response;
  //           console.log(approveCode.value, 'approveCode.value');
  //           approvalModalShow.value = true;
  //         } else {
  //           // 如果没有审批流程，直接跳转到列表页
  //           router.go(-1);
  //         }
  //       }
  //     });
  //     // 生成唯一标识符作为存储键
  //     const storageKey = resourceHotelInquiryId ? `inquiry_${resourceHotelInquiryId}` : `inquiry_${Date.now()}`;

  //     // 保存到sessionStorage (作为本地备份)
  //     sessionStorage.setItem(storageKey, JSON.stringify(submitData));
  //   })
  //   .catch(error => {
  //     console.error('提交失败:', error);
  //     loading.value = false;
  //   })
  //   .finally(() => {
  //     loading.value = false;
  //   });
  // // 显示确认弹窗
  hModal.confirm({
    content: '确定要提交吗？',
    okText: '确定',
    cancelText: '取消',
    onOk: () => {
      priceInquiryApi.productEdit(submitData)
        .then(response => {
          message.success('提交成功')
          router.go(-1)
          // 生成唯一标识符作为存储键
          const storageKey = resourceHotelInquiryId ? `inquiry_${resourceHotelInquiryId}` : `inquiry_${Date.now()}`;

          // 保存到sessionStorage (作为本地备份)
          sessionStorage.setItem(storageKey, JSON.stringify(submitData));
        })
        .catch(error => {
          console.error('提交失败:', error);
          loading.value = false;
        })
        .finally(() => {
          loading.value = false;
        });
    },
  });
};

//酒店详情信息
const hotelDetails = ref()

//查找住宿id
const searchhotelId = (index: number, num: number) => {
  const result = hotelDetails.value.resourceHotelRooms[index].priceResults
    .find((item: { priceItem: number }) => item.priceItem === num);
  return result ?? null; // 安全访问 + 默认值
};

//查找床型早餐
const searchProvide = (type: number) => {
  let provideType = ''
  switch (type) {
    case 1: provideType = '单早'; break;
    case 2: provideType = '双早'; break;
    case 3: provideType = '单早'; break;
  }
  return provideType
};

//查找会场id
const searchVenuesId = (index: number, num: number) => {
  // console.log(index,"hotelDetails.valuesearchVenuesId");
  let result = null
  if (hotelDetails.value.resourceHotelPlaces[index]) {
    result = hotelDetails.value.resourceHotelPlaces[index].priceResults
      .find((item: { priceItem: number }) => item.priceItem === num);
  } else {
    return
  }

  return result ?? null; // 安全访问 + 默认值
};


// 在组件挂载时获取ID
onMounted(async () => {
  // 从路由参数中获取ID和code
  const idFromRoute = route.query.id;
  codeFromRoute.value = route.query.code as string | null;
  console.log(codeFromRoute.value);

  // 检查是否为强制新建模式
  const isNewMode = route.query.isNew === 'true';

  // 如果是新建模式，不加载任何数据，直接显示空表单
  if (isNewMode) {
    console.log('新建模式，显示空表单');
    return;
  }

  if (codeFromRoute.value) {
    try {
      loading.value = true;
      const response = await priceInquiryApi.newGetInquiryDetails({ inquiryCode: codeFromRoute.value } as IPriceInquiryFilter);
      console.log(response, "response");
      if (response) {
        enableQuarter.value = response.enableQuarter || false;
      }
      if (response.enableQuarter && response.enableQuarter?.length > 0) {
        formData.seasonSettingMode = 1
      } else {
        formData.seasonSettingMode = 2
      }
      if (response) {
        hotelDetails.value = response
        console.log();

        // 设置控制显示的变量

        enablePlaceChange.value = response.enablePlaceChange || false;
        isLocal.value = response.isLocal || false
      }
      // 检查询价单状态，如果是初始状态(10)且没有填写过数据，显示空表单
      const responseAny = response as any;
      if (responseAny && responseAny.inquiryState === 10) {
        // 检查是否有已填写的数据
        const hasRoomData = responseAny.resourceHotelRooms && Array.isArray(responseAny.resourceHotelRooms) && responseAny.resourceHotelRooms.length > 0;
        const hasVenueData = responseAny.resourceHotelPlaces && Array.isArray(responseAny.resourceHotelPlaces) && responseAny.resourceHotelPlaces.length > 0;
        const hasQuarterData = responseAny.resourceHotelQuarters && Array.isArray(responseAny.resourceHotelQuarters) && responseAny.resourceHotelQuarters.length > 0;

        // 如果没有任何已填写的数据，显示空表单
        if (!hasRoomData && !hasVenueData && !hasQuarterData) {
          console.log('询价单为初始状态且无数据，显示空表单');
          inquiryId.value = responseAny.id || null;
          enableQuarter.value = responseAny.enableQuarter || false;
          enablePlaceChange.value = responseAny.enablePlaceChange || false;
          loading.value = false;
          return;
        }
      }

      // 处理接口返回的数据
      if (response) {
        // 设置询价单ID和状态
        const responseAny = response as any;
        inquiryId.value = responseAny.id || null;
        platformHotelCode.value = responseAny.platformHotelCode

        // 处理房间数据 - 无论是否有数据都要处理
        // 清空原有数据
        formData.rooms = [];

        if (responseAny.resourceHotelRooms && responseAny.resourceHotelRooms.length > 0) {
          // 遍历处理房间数据
          responseAny.resourceHotelRooms.forEach((room: any, index: number) => {
            // 创建房间基础数据
            const roomData = {
              id: room.id,
              roomType: room.roomType,
              Marketprice: 0,//市场价
              path: null,
              lightSeasonMarketPrice: 0,
              lightSeasonAgreementPriceUnder50: 0,
              lightSeasonAgreementPriceAbove50: 0,
              peakSeasonMarketPrice: 0,
              peakSeasonAgreementPriceUnder50: 0,
              peakSeasonAgreementPriceAbove50: 0,
              marketPrice: 0, // 新增：独立的门市价 (code=30)
              agreementPrice: 0, // 新增：独立的协议价 (code=20)
            };

            // 处理价格数据
            if (room.priceResults && room.priceResults.length > 0) {
              room.priceResults.forEach((price: any) => {
                // 根据priceItem字段映射到对应的价格字段
                switch (price.priceItem) {
                  case 40: // 淡季门市价
                    roomData.lightSeasonMarketPrice = price.price;
                    break;
                  case 50: // 淡季协议价,50人以下
                    roomData.lightSeasonAgreementPriceUnder50 = price.price;
                    break;
                  case 60: // 淡季协议价,50人以上
                    roomData.lightSeasonAgreementPriceAbove50 = price.price;
                    break;
                  case 70: // 旺季门市价
                    roomData.peakSeasonMarketPrice = price.price;
                    break;
                  case 80: // 旺季协议价,50人以下
                    roomData.peakSeasonAgreementPriceUnder50 = price.price;
                    break;
                  case 90: // 旺季协议价,50人以上
                    roomData.peakSeasonAgreementPriceAbove50 = price.price;
                    break;
                  case 30: // 门市价
                    roomData.marketPrice = price.price;
                    break;
                  case 20: // 协议价
                    roomData.agreementPrice = price.price;
                    break;
                  case 10: // 市场价
                    roomData.Marketprice = price.price;
                    roomData.path = price.path;
                    break;
                }
              });
            }

            // 添加到房间数组
            formData.rooms.push(roomData);
            console.log(formData.rooms, "formData.rooms");

          });
        }
        // 如果没有房间数据，保持空数组状态

        // 处理会场数据 - 无论是否有数据都要处理
        // 清空原有数据
        formData.venues = [];

        if (response.resourceHotelPlaces && response.resourceHotelPlaces.length > 0) {
          // 遍历处理会场数据
          response.resourceHotelPlaces.forEach((place: any) => {
            console.log(place, '##1111');
            // 创建会场基础数据
            const venueData = {
              id: place.id,
              platformPlaceId: place.platformPlaceId,
              venueName: place.placeName,
              maxNum: place.maxNum,
              hasColumn: place.isPillar ? '是' : '否',
              area: place.area,
              height: place.height || 0,
              length: place.length,
              width: place.wide, // 注意API中是wide而非width
              floor: place.floor,
              lightSeasonMarketPrice: 0,
              lightSeasonAgreementPrice: 0,
              peakSeasonMarketPrice: 0,
              peakSeasonAgreementPrice: 0,
              vendorSeasonMarketPrice: 0,
              vendorSeasonAgreementPrice: 0,
              vendorSeasonHalfDayMarketPrice: 0,
              vendorSeasonHalfDayAgreementPrice: 0,
              Ordinarymarketprice: 0,// 半天门市价
              Ordinaryagreementprice: 0,//半天协议价
              marketPrice: 0, // 新增：门市价 (code=30)
              agreementPrice: 0, // 新增：协议价 (code=20)
              dayMarketPrice: 0,//全天市场价
              HalfDayMarketPrice: 0,//半天市场价
              venueImages: place.images,
              path: null,
              tableModes: [
                { id: '', mode: 'U型式', supported: '否', maxCapacity: null, image: '' },
                { id: '', mode: '宴会式', supported: '否', maxCapacity: null, image: '' },
                { id: '', mode: '剧院式', supported: '否', maxCapacity: null, image: '' },
                { id: '', mode: '课桌式', supported: '否', maxCapacity: null, image: '' },
                { id: '', mode: '海岛式', supported: '否', maxCapacity: null, image: '' },
                { id: '', mode: '鸡尾酒', supported: '否', maxCapacity: null, image: '' },
                { id: '', mode: '鱼骨式', supported: '否', maxCapacity: null, image: '' }
              ]
            };

            // 处理价格数据
            if (place.priceResults && place.priceResults.length > 0) {
              place.priceResults.forEach(price => {
                // 根据priceItem字段映射到对应的价格字段
                switch (price.priceItem) {
                  case 110: // 淡季全天门市价
                    venueData.lightSeasonMarketPrice = price.price;
                    break;
                  case 130: // 淡季全天协议价
                    venueData.lightSeasonAgreementPrice = price.price;
                    break;
                  case 100: // 淡季半天门市价
                    venueData.peakSeasonMarketPrice = price.price;
                    break;
                  case 120: // 淡季半天协议价
                    venueData.peakSeasonAgreementPrice = price.price;
                    break;
                  case 150: // 旺季全天门市价
                    venueData.vendorSeasonMarketPrice = price.price;
                    break;
                  case 170: // 旺季全天协议价
                    venueData.vendorSeasonAgreementPrice = price.price;
                    break;
                  case 140: // 旺季半天门市价
                    venueData.vendorSeasonHalfDayMarketPrice = price.price;
                    break;
                  case 160: // 旺季半天协议价
                    venueData.vendorSeasonHalfDayAgreementPrice = price.price;
                    break;
                  case 200: // 门市价半天价
                    venueData.Ordinarymarketprice = price.price;
                    break;
                  case 220: // 协议价半天价
                    venueData.Ordinaryagreementprice = price.price;
                    break;
                  case 210: // 门市价
                    venueData.marketPrice = price.price;
                    break;
                  case 230: // 协议价
                    venueData.agreementPrice = price.price;
                    break;
                  case 180: // 全天市场价
                    venueData.dayMarketPrice = price.price;
                    venueData.path = price.path;
                    break;
                  case 190: // 半天协议价
                    venueData.HalfDayMarketPrice = price.price;
                    break;
                }
              });
            }

            // 处理桌型数据
            if (place.tableResults && place.tableResults.length > 0) {
              place.tableResults.forEach(table => {
                // 根据tableType字段映射到对应的桌型
                let modeIndex = -1;
                switch (table.tableType) {
                  case 1: // U型式
                    modeIndex = venueData.tableModes.findIndex(m => m.mode === 'U型式');
                    break;
                  case 2: // 宴会式/董事会式
                    modeIndex = venueData.tableModes.findIndex(m => m.mode === '宴会式');
                    break;
                  case 3: // 剧院式
                    modeIndex = venueData.tableModes.findIndex(m => m.mode === '剧院式');
                    break;
                  case 4: // 海岛式
                    modeIndex = venueData.tableModes.findIndex(m => m.mode === '海岛式');
                    break;
                  case 5: // 鸡尾酒/酒会式
                    modeIndex = venueData.tableModes.findIndex(m => m.mode === '鸡尾酒');
                    break;
                  case 6: // 课桌式
                    modeIndex = venueData.tableModes.findIndex(m => m.mode === '课桌式');
                    break;
                  case 7: // 鱼骨式
                    modeIndex = venueData.tableModes.findIndex(m => m.mode === '鱼骨式');
                    break;
                }

                // 更新桌型数据
                if (modeIndex !== -1) {
                  venueData.tableModes[modeIndex].supported = '是';
                  venueData.tableModes[modeIndex].maxCapacity = table.maxNum;
                  venueData.tableModes[modeIndex].id = table.id;
                  // 获取桌型图片URL - 从tableUrls中获取所有图片
                  if (table.tableUrls && table.tableUrls.length > 0) {
                    // 保存所有图片路径到一个数组中
                    const imageUrls = table.tableUrls[0];
                    console.log(imageUrls, "imageUrls");


                    // 至少保存第一张图片的路径到image字段，用于表单提交
                    venueData.tableModes[modeIndex].image = imageUrls

                    // 如果需要，可以额外添加一个字段保存所有图片路径
                    venueData.tableModes[modeIndex].allImages = imageUrls;
                  }
                }
              });
            }

            // 添加到会场数组
            formData.venues.push(venueData);
            console.log(formData.venues, "formData.venues");

          });
        }
        // 如果没有会场数据，保持空数组状态

        // 处理淡旺季数据 - 无论是否有数据都要处理
        // 清空原有数据
        formData.lightSeasons = [];
        formData.peakSeasons = [];

        if (response.resourceHotelQuarters && response.resourceHotelQuarters.length > 0) {
          // 有淡旺季数据，设置为按淡旺季设置模式
          formData.seasonSettingMode = 1;

          // 分类处理淡季和旺季数据
          response.resourceHotelQuarters.forEach((quarter, index) => {
            const seasonData = {
              id: index + 1,
              startDate: dayjs(quarter.startDate),
              endDate: dayjs(quarter.endDate)
            };

            // 根据season字段分类（1为淡季，2为旺季）
            if (quarter.season === 1) {
              formData.lightSeasons.push(seasonData as any);
            } else if (quarter.season === 2) {
              formData.peakSeasons.push(seasonData as any);
            }
          });
        } else {
          // 没有淡旺季数据，设置为无淡旺季模式
          formData.seasonSettingMode = 2;
        }

        // 如果没有淡季或旺季数据，添加默认空项
        if (formData.lightSeasons.length === 0) {
          formData.lightSeasons.push({
            id: 1,
            startDate: undefined,
            endDate: undefined
          });
        }

        if (formData.peakSeasons.length === 0) {
          formData.peakSeasons.push({
            id: 1,
            startDate: undefined,
            endDate: undefined
          });
        }
      }
      message.success('已成功获取酒店产品信息详情');
    } catch (error) {
      console.error('获取酒店产品信息详情失败:', error);
    } finally {
      loading.value = false;
    }
    return
  } else if (idFromRoute) {
    inquiryId.value = Number(idFromRoute);

    // 获取询价单详情
    try {
      loading.value = true;
      // 使用正确的类型
      const response = await priceInquiryApi.getHotelPriceInquiry({ id: idFromRoute.toString() } as IPriceInquiryFilter);

      // 设置控制显示的变量
      enableQuarter.value = response.enableQuarter || false;
      enablePlaceChange.value = response.enablePlaceChange || false;

      // 处理接口返回的数据 - 与第一个接口保持相同的处理逻辑
      if (response) {
        // 设置询价单ID和状态
        inquiryId.value = response.id || null;

        // 处理房间数据 - 无论是否有数据都要处理
        formData.rooms = [];
        if (response.resourceHotelRooms && response.resourceHotelRooms.length > 0) {
          // 处理房间数据的逻辑与第一个接口相同...
        }

        // 处理会场数据 - 无论是否有数据都要处理
        formData.venues = [];
        if (response.resourceHotelPlaces && response.resourceHotelPlaces.length > 0) {
          // 处理会场数据的逻辑与第一个接口相同...
        }

        // 处理淡旺季数据 - 无论是否有数据都要处理
        formData.lightSeasons = [];
        formData.peakSeasons = [];
        if (response.resourceHotelQuarters && response.resourceHotelQuarters.length > 0) {
          // 有淡旺季数据，设置为按淡旺季设置模式
          formData.seasonSettingMode = 1;
          // 处理淡旺季数据的逻辑与第一个接口相同...
        } else {
          // 没有淡旺季数据，设置为无淡旺季模式
          formData.seasonSettingMode = 2;
        }

        // 添加默认空项
        if (formData.lightSeasons.length === 0) {
          formData.lightSeasons.push({
            id: 1,
            startDate: undefined,
            endDate: undefined
          });
        }
        if (formData.peakSeasons.length === 0) {
          formData.peakSeasons.push({
            id: 1,
            startDate: undefined,
            endDate: undefined
          });
        }
      }

      message.success('已成功获取询价单详情');
    } catch (error) {
      console.error('获取询价单详情失败:', error);
      message.error('获取询价单详情失败');
    } finally {
      loading.value = false;
    }
  }

  // 从缓存中获取会场数据
  try {
    const storageKey = codeFromRoute.value || (route.query.id ? `venues_${route.query.id}` : null);
    if (storageKey) {
      // 尝试从sessionStorage获取
      let venueDataStr = sessionStorage.getItem(storageKey);

      // 如果有数据，就解析并更新表单
      if (venueDataStr) {
        const parsedData = JSON.parse(venueDataStr);
        console.log('从会话存储获取的会场数据:', parsedData);

        // 如果是venues数组对象
        if (parsedData.venues && Array.isArray(parsedData.venues)) {
          formData.venues = parsedData.venues;
          message.success('已加载会话中的会场数据');
        }
        // 如果是单个会场数据（不是数组）
        else if (parsedData && !Array.isArray(parsedData) && parsedData.venueName) {
          // 将单个会场添加到venues数组
          formData.venues = [parsedData];
          message.success('已加载会话中的会场数据');
        }
      } else {
        // 检查是否有完整提交数据
        const inquiryDataStr = sessionStorage.getItem(`inquiry_${storageKey}`);
        if (inquiryDataStr) {
          const inquiryData = JSON.parse(inquiryDataStr);
          if (inquiryData.venues && Array.isArray(inquiryData.venues)) {
            formData.venues = inquiryData.venues;
            message.success('已加载提交记录中的会场数据');
          }
        }
      }
    }
  } catch (error) {
    console.error('获取会场数据失败:', error);
  }

  // 添加页面卸载事件，清除sessionStorage中的数据
  window.addEventListener('beforeunload', clearSessionData);
});

// 页面卸载时清除会话数据
const clearSessionData = () => {
  try {
    const storageKey = codeFromRoute.value || route.query.id;
    if (storageKey) {
      // 清除会场数据
      sessionStorage.removeItem(`venues_${storageKey}`);
      // 清除表单提交数据
      sessionStorage.removeItem(`inquiry_${storageKey}`);
      console.log('已清除会话存储数据');
    }
  } catch (error) {
    console.error('清除会话数据失败:', error);
  }
};

// 组件卸载时移除事件监听器
onUnmounted(() => {
  window.removeEventListener('beforeunload', clearSessionData);
  clearSessionData(); // 主动清除数据
});


// 添加编辑会场函数
const editVenue = (record: any, index: number) => {
  venueTitle.value = '编辑会场'
  // 设置当前编辑的会场索引
  const editingVenue = JSON.parse(JSON.stringify(record));
  console.log(editingVenue, "editingVenue");


  // 设置新会场数据为当前要编辑的会场
  const newVenue = {
    id: editingVenue.id,
    venueName: editingVenue.venueName,
    hasColumn: editingVenue.hasColumn,
    area: editingVenue.area,
    height: editingVenue.height || 0,
    length: editingVenue.length,
    width: editingVenue.width,
    floor: editingVenue.floor,
    lightSeasonMarketPrice: editingVenue.lightSeasonMarketPrice,
    lightSeasonAgreementPrice: editingVenue.lightSeasonAgreementPrice,
    peakSeasonMarketPrice: editingVenue.peakSeasonMarketPrice,
    peakSeasonAgreementPrice: editingVenue.peakSeasonAgreementPrice,
    vendorSeasonMarketPrice: editingVenue.vendorSeasonMarketPrice,
    vendorSeasonAgreementPrice: editingVenue.vendorSeasonAgreementPrice || 0, // 新增：旺季全天协议价
    vendorSeasonHalfDayMarketPrice: editingVenue.vendorSeasonHalfDayMarketPrice || 0, // 新增：旺季半天市场价
    vendorSeasonHalfDayAgreementPrice: editingVenue.vendorSeasonHalfDayAgreementPrice || 0, // 新增：旺季半天协议价
    Ordinarymarketprice: editingVenue.Ordinarymarketprice,//门市价半天价
    Ordinaryagreementprice: editingVenue.Ordinaryagreementprice,//协议价半天价
    marketPrice: editingVenue.marketPrice || 0, // 添加独立的门市价
    agreementPrice: editingVenue.agreementPrice || 0, // 添加独立的协议价
    venueImages: editingVenue.venueImages || [],
    tableModes: editingVenue.tableModes || [
      { mode: 'U型式', supported: '是', maxCapacity: 120, image: null },
      { mode: '宴会式', supported: '是', maxCapacity: 100, image: null },
      { mode: '剧院式', supported: '是', maxCapacity: 200, image: null },
      { mode: '课桌式', supported: '否', maxCapacity: null, image: null },
      { mode: '海岛式', supported: '否', maxCapacity: null, image: null },
      { mode: '鸡尾酒', supported: '否', maxCapacity: null, image: null },
      { mode: '鱼骨式', supported: '否', maxCapacity: null, image: null }
    ]
  };

  formData.newVenue = newVenue
  // 设置价格配置表数据
  // 半天价格
  priceConfigData[0].marketPrice_light = editingVenue.peakSeasonMarketPrice || 0;
  priceConfigData[0].agreementPrice_light = editingVenue.peakSeasonAgreementPrice || 0;
  priceConfigData[0].marketPrice_peak = editingVenue.vendorSeasonHalfDayMarketPrice || 0;
  priceConfigData[0].agreementPrice_peak = editingVenue.vendorSeasonHalfDayAgreementPrice || 0;
  priceConfigData[0].marketPrice = editingVenue.Ordinarymarketprice || 0;
  priceConfigData[0].agreementPrice = editingVenue.Ordinaryagreementprice || 0;

  // 全天价格
  priceConfigData[1].marketPrice_light = editingVenue.lightSeasonMarketPrice || 0;
  priceConfigData[1].agreementPrice_light = editingVenue.lightSeasonAgreementPrice || 0;
  priceConfigData[1].marketPrice_peak = editingVenue.vendorSeasonMarketPrice || 0;
  priceConfigData[1].agreementPrice_peak = editingVenue.vendorSeasonAgreementPrice || 0;
  priceConfigData[1].marketPrice = editingVenue.marketPrice || 0;
  priceConfigData[1].agreementPrice = editingVenue.agreementPrice || 0;

  // 设置图片上传列表
  venueImageFileList.value = editingVenue.venueImages.map((url: any, idx: number) => ({
    uid: `-${idx}`,
    name: `图片${idx + 1}`,
    status: 'done',
    url: url.path,
    filePath: url.path
  }));
  console.log(venueImageFileList.value, "5959595");


  // 设置桌型图片
  if (editingVenue.tableModes) {
    // 清空现有的桌型图片列表
    Object.keys(tableImageFileList.value).forEach(key => {
      tableImageFileList.value[key] = [];
    });

    // 设置桌型图片
    editingVenue.tableModes.forEach((mode: any) => {
      if (mode.image) {
        tableImageFileList.value[mode.mode] = [{
          uid: `-1`,
          name: `${mode.mode}图片`,
          status: 'done',
          url: mode.image.path,
          filePath: mode.image.path
        }];
      }
    });
  }

  // 记录当前编辑的会场索引，用于确认时更新
  formData.editingVenueIndex = index;

  // 显示弹框
  formData.venueFormVisible = true;

  console.log(formData.newVenue, "formData.newVenue");

};

// 添加删除会场函数
const removeVenue = (record: any, index: number) => {
  hModal.confirm({
    title: '确认删除',
    content: `确定要删除会场 "${record.venueName}" 吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      formData.venues.splice(index, 1);
      message.success('删除会场成功');


      // 更新本地存储
      const resourceHotelInquiryId = route.query.id ? String(route.query.id) : `venue_${Date.now()}`;
      const allVenuesData = {
        venues: formData.venues
      };
      sessionStorage.setItem(`venues_${resourceHotelInquiryId}`, JSON.stringify(allVenuesData));
    }
  });
};

const handleCancel = () => {
  router.go(-1)
}

const vSelectOnFocus = {
  mounted(el: HTMLElement) {
    const input = el.querySelector('input')
    const handleFocus = (e: Event) => {
      setTimeout(() => (e.target as HTMLInputElement).select(), 10)
    }
    input?.addEventListener('focus', handleFocus)
    // 存储回调函数以便卸载时移除
    el._selectOnFocusHandler = handleFocus
  },
  unmounted(el: HTMLElement & { _selectOnFocusHandler?: EventListener }) {
    const input = el.querySelector('input')
    if (el._selectOnFocusHandler) {
      input?.removeEventListener('focus', el._selectOnFocusHandler)
    }
  }
}
</script>
<template>
  <div class="container">
    <div class="sub-section" v-if="isLocal">
      <h3 class="sub-section-title">住宿：</h3>
      <h-table :columns="roomColumns" :dataSource="formData.rooms" :pagination="false"
        :rowKey="record => record.id.toString()" bordered>
        <template #bodyCell="{ column, index }">
          <template v-if="column.dataIndex === 'lightSeasonMarketPrice'">
            <h-input-number v-model:value="formData.rooms[index].lightSeasonMarketPrice" :min="0" placeholder="请输入"
              style="width: 100%" required :precision="2" addon-after="元" v-select-on-focus/>
          </template>
          <template v-if="column.dataIndex === 'lightSeasonAgreementPriceUnder50'">
            <h-input-number v-model:value="formData.rooms[index].lightSeasonAgreementPriceUnder50" :min="0"
              placeholder="请输入" style="width: 100%" required :precision="2" addon-after="元" v-select-on-focus/>
          </template>
          <template v-if="column.dataIndex === 'lightSeasonAgreementPriceAbove50'">
            <h-input-number v-model:value="formData.rooms[index].lightSeasonAgreementPriceAbove50" :min="0"
              placeholder="请输入" style="width: 100%" required :precision="2" addon-after="元" v-select-on-focus/>
          </template>
          <template v-if="column.dataIndex === 'peakSeasonMarketPrice'">
            <h-input-number v-model:value="formData.rooms[index].peakSeasonMarketPrice" :min="0" placeholder="请输入"
              style="width: 100%" required :precision="2" addon-after="元" v-select-on-focus/>
          </template>
          <template v-if="column.dataIndex === 'peakSeasonAgreementPriceUnder50'">
            <h-input-number v-model:value="formData.rooms[index].peakSeasonAgreementPriceUnder50" :min="0"
              placeholder="请输入" style="width: 100%" :precision="2" addon-after="元" v-select-on-focus/>
          </template>
          <template v-if="column.dataIndex === 'peakSeasonAgreementPriceAbove50'">
            <h-input-number v-model:value="formData.rooms[index].peakSeasonAgreementPriceAbove50" :min="0"
              placeholder="请输入" style="width: 100%" :precision="2" addon-after="元" v-select-on-focus/>
          </template>
          <template v-if="column.dataIndex === 'marketPrice'">
            <h-input-number v-model:value="formData.rooms[index].marketPrice" :min="0" placeholder="请输入"
              style="width: 100%" required :rules="[
                { required: true, message: '必填', trigger: ['blur', 'change'] },
                { type: 'number', min: 0, message: '需≥0' }
              ]" :precision="2" addon-after="元" v-select-on-focus/>
          </template>
          <template v-if="column.dataIndex === 'agreementPrice'">
            <h-input-number v-model:value="formData.rooms[index].agreementPrice" :min="0" placeholder="请输入"
              style="width: 100%" :precision="2" addon-after="元" v-select-on-focus/>
          </template>
        </template>
      </h-table>
    </div>

    <!-- 会场价格表 -->
    <div class="sub-section">
      <h3 class="sub-section-title">会场：</h3>
      <h-table :columns="venueColumns" :dataSource="formData.venues" :pagination="false"
        :rowKey="record => record.venueName" :scroll="{ x: formData.seasonSettingMode === 1 ? 1800 : 1200 }" bordered>
        <template #bodyCell="{ column, record }">
          <!-- 所有列都以纯文本形式展示，不允许修改 -->
          <template v-if="column.dataIndex === 'lightSeasonMarketPrice'">
            {{ record.lightSeasonMarketPrice.toFixed(2)+'元' }}
          </template>
          <template v-if="column.dataIndex === 'lightSeasonAgreementPrice'">
            {{ record.lightSeasonAgreementPrice.toFixed(2)+'元' }}
          </template>
          <template v-if="column.dataIndex === 'peakSeasonMarketPrice'">
            {{ record.peakSeasonMarketPrice.toFixed(2)+'元' }}
          </template>
          <template v-if="column.dataIndex === 'peakSeasonAgreementPrice'">
            {{ record.peakSeasonAgreementPrice.toFixed(2)+'元' }}
          </template>
          <template v-if="column.dataIndex === 'vendorSeasonMarketPrice'">
            {{ record.vendorSeasonMarketPrice.toFixed(2)+'元' }}
          </template>
          <template v-if="column.dataIndex === 'vendorSeasonAgreementPrice'">
            {{ record.vendorSeasonAgreementPrice.toFixed(2)+'元' }}
          </template>
          <template v-if="column.dataIndex === 'vendorSeasonHalfDayMarketPrice'">
            {{ record.vendorSeasonHalfDayMarketPrice.toFixed(2)+'元' }}
          </template>
          <template v-if="column.dataIndex === 'vendorSeasonHalfDayAgreementPrice'">
            {{ record.vendorSeasonHalfDayAgreementPrice.toFixed(2)+'元' }}
          </template>
          <template v-if="column.dataIndex === 'marketPrice'">
            {{ record.marketPrice.toFixed(2)+'元' }}
          </template>
          <template v-if="column.dataIndex === 'agreementPrice'">
            {{ record.agreementPrice.toFixed(2)+'元' }}
          </template>
        </template>
      </h-table>
      <div class="venue-actions">
        <h-button type="primary" ghost @click="addVenue">新增会场</h-button>
      </div>

      <div class="footer-actions">
        <div class="submit">
          <h-button style="margin-right: 20px;" @click="handleCancel">取消</h-button>
          <h-button type="primary" @click="submitForm">提交</h-button>
        </div>
      </div>

      <!-- 新增会场弹框 -->
      <h-modal :title="venueTitle" :visible="formData.venueFormVisible" @cancel="cancelAddVenue" @ok="confirmAddVenue"
        okText="确认" cancelText="取消" width="800px" class="venue-model" style="position: relative; z-index: 5;">
        <h-form layout="horizontal" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <h-row :gutter="16">
            <!-- 第一行：会场名称 & 是否有柱 -->
            <h-col :span="12">
              <h-form-item label="会场名称" required>
                <h-input v-model:value="formData.newVenue.venueName" placeholder="请输入会场名称" />
              </h-form-item>
            </h-col>
            <h-col :span="12">
              <h-form-item label="是否有柱" required>
                <h-select v-model:value="formData.newVenue.hasColumn">
                  <h-select-option value="是">是</h-select-option>
                  <h-select-option value="否">否</h-select-option>
                </h-select>
              </h-form-item>
            </h-col>
          </h-row>

          <h-row :gutter="16">
            <!-- 第二行：面积 & 层高 -->
            <h-col :span="12">
              <h-form-item label="面积" required>
                <h-input-number v-model:value="formData.newVenue.area" :min="0" style="width: 100%"
                  :addon-after="'㎡'" :precision="2"/>
              </h-form-item>
            </h-col>
            <h-col :span="12">
              <h-form-item label="层高" required>
                <h-input-number v-model:value="formData.newVenue.height" :min="0" :step="0.1" style="width: 100%"
                  :addon-after="'m'" :precision="2"/>
              </h-form-item>
            </h-col>
          </h-row>

          <h-row :gutter="16">
            <!-- 第三行：长度 & 宽度 -->
            <h-col :span="12">
              <h-form-item label="长度" required>
                <h-input-number v-model:value="formData.newVenue.length" :min="0" :step="0.1" style="width: 100%"
                  :addon-after="'m'" :precision="2"/>
              </h-form-item>
            </h-col>
            <h-col :span="12">
              <h-form-item label="宽度" required>
                <h-input-number v-model:value="formData.newVenue.width" :min="0" :step="0.1" style="width: 100%"
                  :addon-after="'m'" :precision="2"/>
              </h-form-item>
            </h-col>
          </h-row>

          <h-row :gutter="16">
            <!-- 第四行：楼层 -->
            <h-col :span="12">
              <h-form-item label="楼层" required>
                <h-input-number v-model:value="formData.newVenue.floor" :min="1" style="width: 100%"
                  :addon-after="'层'" :precision="0"/>
              </h-form-item>
            </h-col>
          </h-row>


          <!-- 价格配置表格 -->
          <h-form-item :wrapper-col="{ span: 24 }" v-if="isLocal">
            <div class="price-config-table">
              <div class="price-config-title">价格配置</div>
              <h-table :columns="priceConfigColumns" :dataSource="priceConfigData" :pagination="false" bordered>
                <template #bodyCell="{ column, record }">
                  <template v-if="column.dataIndex === 'marketPrice_light'">
                    <h-input-number :value="record.marketPrice_light" :min="0" style="width: 100%" placeholder="请输入"
                      required type="number" @change="(value) => { record.marketPrice_light = value; }" :precision="2" addon-after="元" v-select-on-focus/>
                  </template>
                  <template v-if="column.dataIndex === 'agreementPrice_light'">
                    <h-input-number :value="record.agreementPrice_light" :min="0" style="width: 100%" placeholder="请输入"
                      type="number" @change="(value) => { record.agreementPrice_light = value; }" required :precision="2" addon-after="元" v-select-on-focus/>
                  </template>
                  <template v-if="column.dataIndex === 'marketPrice_peak'">
                    <h-input-number :value="record.marketPrice_peak" :min="0" style="width: 100%" placeholder="请输入"
                      required type="number" @change="(value) => { record.marketPrice_peak = value; }" :precision="2" addon-after="元" v-select-on-focus/>
                  </template>
                  <template v-if="column.dataIndex === 'agreementPrice_peak'">
                    <h-input-number :value="record.agreementPrice_peak" :min="0" style="width: 100%" placeholder="请输入"
                      required type="number" @change="(value) => { record.agreementPrice_peak = value; }" :precision="2" addon-after="元" v-select-on-focus/>
                  </template>
                  <template v-if="column.dataIndex === 'marketPrice'">
                    <h-input-number :value="record.marketPrice" :min="0" style="width: 100%" placeholder="请输入" required
                      type="number" @change="(value) => { record.marketPrice = value; }" :precision="2" addon-after="元" v-select-on-focus/>
                  </template>
                  <template v-if="column.dataIndex === 'agreementPrice'">
                    <h-input-number :value="record.agreementPrice" :min="0" style="width: 100%" placeholder="请输入"
                      required type="number" @change="(value) => { record.agreementPrice = value; }" :precision="2" addon-after="元" v-select-on-focus/>
                  </template>
                </template>
              </h-table>
            </div>
          </h-form-item>


          <!-- 会场图片上传 -->
          <h-form-item>
            <div class="price-config-title">会场图片</div>
            <div class="venue-images-upload">
              <h-upload list-type="picture-card" :file-list="venueImageFileList" :before-upload="beforeUpload"
                :custom-request="uploadRequest" @preview="handlePreview" @remove="handleImageRemove"
                accept=".jpg,.jpeg,.png" multiple>
                <div v-if="venueImageFileList.length < 5">
                  <plus-outlined />
                  <div style="margin-top: 8px">上传</div>
                </div>
              </h-upload>
            </div>
            <div class="upload-hint">建议上传图片的尺寸为400*400 ，大小不超过2MB ，格式为png/jpg/jpeg的文件，最少上传2张。</div>
          </h-form-item>

          <!-- 桌型配置 -->
          <h-form-item>
            <div class="price-config-title">桌型配置</div>
            <div class="upload-hint">建议上传图片的尺寸为400*400 ，大小不超过2MB ，格式为png/jpg/jpeg的文件。</div>
            <div class="table-config">
              <table class="table-mode-table">
                <thead>
                  <tr>
                    <th>桌型</th>
                    <th>是否支持</th>
                    <th>最大可容纳人数</th>
                    <th>桌型图片</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(mode, index) in formData.newVenue.tableModes" :key="mode.mode">
                    <td>{{ mode.mode }}</td>
                    <td>
                      <h-select v-model:value="mode.supported" style="width: 80px"
                        @change="(value) => handleTableModeChange(value, index)">
                        <h-select-option value="是">是</h-select-option>
                        <h-select-option value="否">否</h-select-option>
                      </h-select>
                    </td>
                    <td>
                      <h-input-number v-if="mode.supported === '是'" v-model:value="mode.maxCapacity" :min="1"
                        style="width: 100px" :precision="0"/>
                      <span v-else>-</span>
                    </td>
                    <td>
                      <div v-if="mode.supported === '是'" class="table-image-upload-wrapper">
                        <h-upload :file-list="tableImageFileList[mode.mode]"
                          :custom-request="(options) => uploadTableImage(options, mode.mode)"
                          :before-upload="beforeUpload" @remove="(file) => handleTableImageRemove(file, mode.mode)"
                          @preview="handlePreview" accept=".jpg,.jpeg,.png" :maxCount="1" list-type="picture"
                          class="table-image-upload">
                          <h-button>
                            <upload-outlined /> 上传
                          </h-button>
                        </h-upload>
                      </div>
                      <span v-else>-</span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </h-form-item>
        </h-form>
      </h-modal>

      <!-- 添加独立的预览弹窗 -->
      <h-model class="global-preview-overlay" v-if="previewVisible" @click="previewVisible = false">
        <div class="global-preview-container" @click.stop>
          <div class="global-preview-header">
            <span>{{ previewTitle }}</span>
            <button class="global-preview-close" @click="previewVisible = false">×</button>
          </div>
          <div class="global-preview-main">
            <img :src="previewImage" alt="预览图片" />
          </div>
          <div class="global-preview-nav" v-if="previewImages.length > 1">
            <div v-for="(img, i) in previewImages" :key="i" class="global-preview-thumbnail"
              :class="{ active: img.src === previewImage }" @click="previewImage = img.src">
              <img :src="img.src" :alt="img.title" />
            </div>
          </div>
        </div>
      </h-model>
      <!-- 审批流程模态框 -->
      <h-modal v-model:open="approvalModalShow" title="已提交如下人员审批" width="80%" :keyboard="false" :maskClosable="false"
        :closable="false">
        <div>
          <iframe width="100%" :src="businessProcess + '?code=' + approveCode + '#/detailsPcSt'"
            frameborder="0"></iframe>
        </div>
        <template #footer>
          <h-button @click="
            approvalModalShow = false;
          router.go(-1);
          ">确定</h-button>
        </template>
      </h-modal>
    </div>
  </div>

</template>
<style lang="less" scoped>
.container {
  min-height: calc(100vh - 112px);
  background-color: #fff;
  padding: 20px;
  padding-bottom: 40px;
  border-radius: 10px;
}

.inquiry-order-form {
  width: 1100px;
  margin: 0 auto;
  background-color: #fff;
  /* 增加顶层组件的左右内边距 */
  min-height: calc(100vh - 120px);
  position: relative;
}

.section-container {
  background-color: #fff;
  /* 增加左右内边距，从20px改为40px */
  margin-bottom: 60px;
  max-width: 1400px;
  /* 控制最大宽度 */
  margin-left: auto;
  /* 水平居中 */
  margin-right: auto;
  /* 水平居中 */
}

/* 酒店介绍部分的样式 */
.hotel-intro-section {
  width: 1100px;
  margin: 0 auto;
  margin-bottom: 30px;
  font-size: 14px;
  line-height: 1.8;
  color: #333;
}

.greeting-line {
  margin-top: 48px;
  margin-bottom: 10px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 22px;
  color: #1868DB;
  line-height: 30px;
  text-align: left;
  font-style: normal;
}

.intro-paragraph {
  margin-bottom: 15px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 16px;
  color: #1D2129;
  line-height: 28px;
  text-align: left;
  font-style: normal;
}

.cooperation-details {
  width: 936px;

  .detail-title {
    font-weight: bold;
    margin-bottom: 5px;
  }

  .detail-desc {
    margin-bottom: 10px;
  }

  .product-list {
    padding-left: 15px;
    background: rgba(24, 104, 219, 0.04);
    border-radius: 8px;
    border: 1px solid rgba(24, 104, 219, 0.1);

    .product-type {
      font-weight: bold;
      margin-top: 10px;
    }

    .product-item {
      padding-left: 20px;
      margin-bottom: 5px;
    }
  }
}

.section-title {
  display: flex;
  align-items: center;
  gap: 10px;
  /* 控制横线与文字的间距 */
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #333;
}

.section-title::before,
.section-title::after {
  content: "";
  flex: 1;
  /* 自动拉伸横线长度 */
  height: 1px;
  background: #ccc;
  /* 横线颜色 */
}

.section-title::after {
  flex: 30;
}

.sub-section {
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0px 3px 12px 0px rgba(1, 12, 51, 0.145);
  margin-bottom: 30px;
}

.label-line {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.sub-section-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #333;
}

.label-with-asterisk {
  &::before {
    content: "*";
    color: #ff4d4f;
    margin-right: 4px;
  }

  min-width: 140px;
}

.season-row,
.tier-row {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.season-label,
.tier-label {
  width: 120px;
  margin-right: 10px;
  text-align: right;
}

.season-separator,
.tier-separator {
  margin: 0 10px;
}

.link-button {
  margin-right: 10px;
  cursor: pointer;
}

.primary-color {
  color: #1890ff;
}

.remark {
  color: #f5222d;
  font-size: 12px;
  margin-bottom: 10px;
}

.venue-actions {
  margin-top: 15px;
  display: flex;
  justify-content: flex-start;
}

/* 操作指引样式 */
.operation-guide {
  margin: 30px 0;
  border-top: 1px dashed #e8e8e8;
  padding-top: 20px;
  padding-bottom: 100px;
}

.guide-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #333;
  display: flex;
  align-items: center;

  img {
    width: 16px;
    height: 16px;
    vertical-align: middle;
  }
}

.guide-text {
  line-height: 1.8;
  color: #333;
}

.Indentation {
  margin-bottom: 32px;
  text-indent: 1em;
}

.bottom {
  width: 1084px;
  height: 120px;
  background: rgba(24, 104, 219, 0.08);
  border-radius: 8px;
  border: 1px solid rgba(24, 104, 219, 0.5);
  display: flex;
  align-items: center;
  flex-wrap: wrap;

  p {
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 16px;
    color: #1D2129;
    text-align: left;
    font-style: normal;
    margin-bottom: 0;
    margin-left: 15px;
  }
}

.highlight {
  color: #1890ff;
  font-weight: 500;
}

.footer-actions {
  position: fixed;
  bottom: 0;
  right: 15px;
  background-color: #fff;
  padding: 10px 20px;
  display: flex;
  justify-content: center;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.15);
  z-index: 10;
  width: calc(100% - 245px);
  /* 控制最大宽度，与内容区域保持一致 */
  margin: 0 auto;
  /* 水平居中 */

  .submit {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: right;

    p {
      margin-bottom: 0;
      line-height: 20px;
      text-align: left;
      font-style: normal;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      margin-right: 12px;

      img {
        width: 16px;
        height: 16px;
      }

      &:nth-child(1) {
        color: #1D2129;
      }

      &:nth-child(2) {
        color: #86909C;
        margin-right: 24px;
      }
    }
  }
}


/* 隐藏年份选择器的样式 */
:deep(.hide-year-picker) {

  /* 隐藏所有原生的头部组件 */
  .ant-picker-header button,
  .ant-picker-header-view {
    display: none !important;
  }

  /* 覆盖原有样式 */
  .ant-picker-header {
    border-bottom: 1px solid #f0f0f0;

    .custom-month-header {
      button:hover {
        color: #1890ff;
      }
    }
  }
}

.unit-label {
  margin-left: 5px;
  color: #666;
}

.price-config-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 12px;
  color: #333;
  padding-left: 2px;
}

.price-config-table {
  margin-bottom: 15px;
}

.venue-images-upload {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 10px;
}

.upload-hint {
  color: #999;
  font-size: 12px;
  margin-top: 5px;
}

.table-config {
  margin-top: 10px;
}

.table-mode-table {
  width: 100%;
  border-collapse: collapse;
}

.table-mode-table th,
.table-mode-table td {
  padding: 8px;
  text-align: left;
  border-bottom: 1px solid #f0f0f0;
}

.table-mode-table th {
  background-color: #fafafa;
}

.table-image-upload-wrapper {
  display: flex;
  align-items: center;

  :deep(.ant-upload-select) {
    margin-right: 10px;
  }

  :deep(.ant-upload-list) {
    display: flex;
    margin-top: 0;
  }

  :deep(.ant-upload-list-picture) {
    .ant-upload-list-item {
      margin-top: 0;
      margin-right: 8px;
      padding: 4px;
      height: auto;
    }
  }
}

.table-image-upload {
  :deep(.ant-upload-list-picture .ant-upload-list-item-thumbnail) {
    width: 50px;
    height: 50px;
  }

  :deep(.ant-upload-list-picture .ant-upload-list-item) {
    height: 50px;
    padding: 8px;
    margin-top: 8px;

    .ant-upload-list-item-name {
      display: none !important;
    }

    .ant-upload-list-item-card-actions {
      position: absolute;
      right: 5px;
      top: 5px;
    }
  }
}

.image-preview-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.image-preview-main {
  width: 100%;
  text-align: center;
  margin-bottom: 16px;
}

.image-preview-nav {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
}

.image-preview-thumbnail {
  width: 60px;
  height: 60px;
  border: 2px solid transparent;
  cursor: pointer;
  overflow: hidden;

  &.active {
    border-color: #1890ff;
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  &:hover {
    opacity: 0.8;
  }
}

/* 全局预览弹窗样式 */
.global-preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 99999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.global-preview-container {
  background-color: white;
  border-radius: 4px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.global-preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
}

.global-preview-close {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #999;
}

.global-preview-main {
  padding: 16px;
  text-align: center;
  overflow: auto;
  flex: 1;

  img {
    max-width: 100%;
    max-height: 60vh;
    object-fit: contain;
  }
}

.global-preview-nav {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 16px;
  justify-content: center;
  border-top: 1px solid #eee;
  background-color: #f9f9f9;
}

.global-preview-thumbnail {
  width: 60px;
  height: 60px;
  border: 2px solid transparent;
  cursor: pointer;
  overflow: hidden;

  &.active {
    border-color: #1890ff;
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  &:hover {
    opacity: 0.8;
  }
}

:deep(.venue-model) {
  position: relative;
  z-index: 5;
}
</style>